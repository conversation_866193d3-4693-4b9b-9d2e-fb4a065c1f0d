{"config": {"filename": "FileScopeMCP-tree-ebbinghaus-backend.json", "baseDirectory": "E:/JYZS/ebbinghaus-backend", "projectRoot": "E:/JYZS/ebbinghaus-backend", "lastUpdated": "2025-08-03T13:26:33.914Z"}, "fileTree": {"path": "E:\\JYZS\\ebbinghaus-backend", "name": "ebbinghaus-backend", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\.env", "name": ".env", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\.env.development", "name": ".env.development", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\.env.example", "name": ".env.example", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\.env.production", "name": ".env.production", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\.env.test", "name": ".env.test", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage", "name": "coverage", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\base.css", "name": "base.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\block-navigation.js", "name": "block-navigation.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\favicon.png", "name": "favicon.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report", "name": "lcov-report", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\base.css", "name": "base.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\block-navigation.js", "name": "block-navigation.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\favicon.png", "name": "favicon.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\prettify.css", "name": "prettify.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\prettify.js", "name": "prettify.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\sort-arrow-sprite.png", "name": "sort-arrow-sprite.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\sorter.js", "name": "sorter.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src", "name": "src", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\app.js.html", "name": "app.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config", "name": "config", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\database.js.html", "name": "database.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\environment.js.html", "name": "environment.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\logger.js.html", "name": "logger.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\performance.js.html", "name": "performance.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\redis.js.html", "name": "redis.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\config\\swagger.js.html", "name": "swagger.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers", "name": "controllers", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers\\analyticsController.js.html", "name": "analyticsController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers\\authController.js.html", "name": "authController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers\\reviewController.js.html", "name": "reviewController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\controllers\\taskController.js.html", "name": "taskController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware", "name": "middleware", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware\\auth.js.html", "name": "auth.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware\\errorHandler.js.html", "name": "errorHandler.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware\\rateLimit.js.html", "name": "rateLimit.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\middleware\\validation.js.html", "name": "validation.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models", "name": "models", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models\\LearningRecord.js.html", "name": "LearningRecord.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models\\ReviewSchedule.js.html", "name": "ReviewSchedule.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models\\Task.js.html", "name": "Task.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\models\\User.js.html", "name": "User.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes", "name": "routes", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\analytics.js.html", "name": "analytics.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\auth.js.html", "name": "auth.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\docs.js.html", "name": "docs.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\reviews.js.html", "name": "reviews.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\routes\\tasks.js.html", "name": "tasks.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services", "name": "services", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services\\authService.js.html", "name": "authService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services\\ebbinghausService.js.html", "name": "ebbinghausService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services\\loadBalanceService.js.html", "name": "loadBalanceService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\services\\taskService.js.html", "name": "taskService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\utils", "name": "utils", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\utils\\constants.js.html", "name": "constants.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\utils\\helpers.js.html", "name": "helpers.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov-report\\src\\utils\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\lcov.info", "name": "lcov.info", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\prettify.css", "name": "prettify.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\prettify.js", "name": "prettify.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\sort-arrow-sprite.png", "name": "sort-arrow-sprite.png", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\sorter.js", "name": "sorter.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src", "name": "src", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\app.js.html", "name": "app.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config", "name": "config", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\database.js.html", "name": "database.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\environment.js.html", "name": "environment.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\logger.js.html", "name": "logger.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\performance.js.html", "name": "performance.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\redis.js.html", "name": "redis.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\config\\swagger.js.html", "name": "swagger.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers", "name": "controllers", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers\\analyticsController.js.html", "name": "analyticsController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers\\authController.js.html", "name": "authController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers\\reviewController.js.html", "name": "reviewController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\controllers\\taskController.js.html", "name": "taskController.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware", "name": "middleware", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware\\auth.js.html", "name": "auth.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware\\errorHandler.js.html", "name": "errorHandler.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware\\rateLimit.js.html", "name": "rateLimit.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\middleware\\validation.js.html", "name": "validation.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models", "name": "models", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models\\LearningRecord.js.html", "name": "LearningRecord.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models\\ReviewSchedule.js.html", "name": "ReviewSchedule.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models\\Task.js.html", "name": "Task.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\models\\User.js.html", "name": "User.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes", "name": "routes", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\analytics.js.html", "name": "analytics.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\auth.js.html", "name": "auth.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\docs.js.html", "name": "docs.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\reviews.js.html", "name": "reviews.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\routes\\tasks.js.html", "name": "tasks.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services", "name": "services", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services\\authService.js.html", "name": "authService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services\\ebbinghausService.js.html", "name": "ebbinghausService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services\\loadBalanceService.js.html", "name": "loadBalanceService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\services\\taskService.js.html", "name": "taskService.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\utils", "name": "utils", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\utils\\constants.js.html", "name": "constants.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\utils\\helpers.js.html", "name": "helpers.js.html", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\coverage\\src\\utils\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\docs", "name": "docs", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\docs\\API.md", "name": "API.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\docs\\API快速参考.md", "name": "API快速参考.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\docs\\swagger-validation-report.json", "name": "swagger-validation-report.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\docs\\SWAGGER_SETUP.md", "name": "SWAGGER_SETUP.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\docs\\后端接口.md", "name": "后端接口.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\docs\\服务器启动指南.md", "name": "服务器启动指南.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\ecosystem.config.js", "name": "ecosystem.config.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\jest.config.js", "name": "jest.config.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\logs", "name": "logs", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\logs\\.a9826a81bd0a53dae5fa25eb83576f18774a9171-audit.json", "name": ".a9826a81bd0a53dae5fa25eb83576f18774a9171-audit.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\logs\\.f061ad709625724e064f5ead54e924ca25eb8ef3-audit.json", "name": ".f061ad709625724e064f5ead54e924ca25eb8ef3-audit.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\logs\\combined-2025-08-03.log", "name": "combined-2025-08-03.log", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\logs\\error-2025-08-03.log", "name": "error-2025-08-03.log", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\package-lock.json", "name": "package-lock.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\package.json", "name": "package.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\PRODUCTION.md", "name": "PRODUCTION.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\README.md", "name": "README.md", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts", "name": "scripts", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\backup.js", "name": "backup.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [{"name": "child_process", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\child_process"}, {"name": "fs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\fs"}, {"name": "path", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\path"}, {"name": "util", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\util"}, {"name": "dotenv", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\dotenv", "version": "^17.2.1"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\deploy.sh", "name": "deploy.sh", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\health-check.js", "name": "health-check.js", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "dotenv", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\dotenv", "version": "^17.2.1"}, {"name": "http", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\http"}, {"name": "child_process", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\child_process"}, {"name": "util", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\util"}], "dependents": [], "summary": "智能健康检查脚本 - 全面检查服务器状态，包括进程、端口、API响应、数据库连接等，提供健康评分和详细状态报告"}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\migrate.js", "name": "migrate.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose", "version": "^8.17.0"}, {"name": "path", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\path"}, {"name": "fs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\fs"}, {"name": "dotenv", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\dotenv", "version": "^17.2.1"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\restore.js", "name": "restore.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [{"name": "child_process", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\child_process"}, {"name": "fs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\fs"}, {"name": "path", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\path"}, {"name": "util", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\util"}, {"name": "readline", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\readline"}, {"name": "dotenv", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\dotenv", "version": "^17.2.1"}, {"name": "mongoose", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose", "version": "^8.17.0"}, {"name": "redis", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\redis", "version": "^5.7.0"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\start-production.sh", "name": "start-production.sh", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\start-server.js", "name": "start-server.js", "isDirectory": false, "importance": 8, "dependencies": [], "packageDependencies": [{"name": "dotenv", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\dotenv", "version": "^17.2.1"}, {"name": "child_process", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\child_process"}, {"name": "fs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\fs"}, {"name": "path", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\path"}, {"name": "util", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\util"}, {"name": "mongoose", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose", "version": "^8.17.0"}, {"name": "redis", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\redis", "version": "^5.7.0"}, {"name": "http", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\http"}], "dependents": [], "summary": "智能服务器启动脚本 - 自动检查前置条件、处理端口冲突、验证数据库连接，提供智能化的服务器启动和监控功能"}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\troubleshoot.js", "name": "troubleshoot.js", "isDirectory": false, "importance": 6, "dependencies": [], "packageDependencies": [{"name": "dotenv", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\dotenv", "version": "^17.2.1"}, {"name": "child_process", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\child_process"}, {"name": "fs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\fs"}, {"name": "path", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\path"}, {"name": "util", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\util"}], "dependents": [], "summary": "故障排除诊断脚本 - 自动检测系统配置、环境变量、依赖包、数据库服务等问题，提供智能化的问题诊断和解决方案建议"}, {"path": "E:\\JYZS\\ebbinghaus-backend\\scripts\\validate-swagger.js", "name": "validate-swagger.js", "isDirectory": false, "importance": 4, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\swagger.js"], "packageDependencies": [{"name": "fs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\fs"}, {"name": "path", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\path"}], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src", "name": "src", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "name": "app.js", "isDirectory": false, "importance": 10, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\docs.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "dotenv", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\dotenv", "version": "^17.2.1"}, {"name": "express", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\express", "version": "^5.1.0"}, {"name": "cors", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\cors", "version": "^2.8.5"}, {"name": "helmet", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\helmet", "version": "^8.1.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\server.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\health.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config", "name": "config", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js", "name": "database.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose", "version": "^8.17.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\server.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\environment.js", "name": "environment.js", "isDirectory": false, "importance": 6, "dependencies": [], "packageDependencies": [{"name": "path", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\path"}, {"name": "fs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\fs"}, {"name": "dotenv", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\dotenv", "version": "^17.2.1"}, {"name": "dotenv", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\dotenv", "version": "^17.2.1"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\performance.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js", "name": "logger.js", "isDirectory": false, "importance": 8, "dependencies": [], "packageDependencies": [{"name": "winston", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\winston", "version": "^3.17.0"}, {"name": "winston-daily-rotate-file", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\winston-daily-rotate-file", "version": "^5.0.0"}, {"name": "path", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\path"}, {"name": "fs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\fs"}, {"name": "morgan", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\morgan", "version": "^1.10.1"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\taskController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\server.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\performance.js", "name": "performance.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\environment.js"], "packageDependencies": [{"name": "compression", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\compression"}, {"name": "helmet", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\helmet", "version": "^8.1.0"}, {"name": "os", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\os"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js", "name": "redis.js", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [{"name": "i<PERSON>is", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\ioredis", "version": "^5.7.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\server.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\config\\swagger.js", "name": "swagger.js", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "swagger-jsdoc", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\swagger-jsdoc", "version": "^6.2.8"}, {"name": "swagger-ui-express", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\swagger-ui-express", "version": "^5.0.1"}, {"name": "path", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\path"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\scripts\\validate-swagger.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\docs.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\controllers", "name": "controllers", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "name": "analyticsController.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\LearningRecord.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "name": "authController.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "name": "reviewController.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\taskController.js", "name": "taskController.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\middleware", "name": "middleware", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "name": "auth.js", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js", "name": "errorHandler.js", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "name": "rateLimit.js", "isDirectory": false, "importance": 10, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [{"name": "express-rate-limit", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\express-rate-limit", "version": "^8.0.1"}, {"name": "express-rate-limit", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\express-rate-limit", "version": "^8.0.1"}, {"name": "rate-limit-redis", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\rate-limit-redis", "version": "^4.2.1"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "name": "validation.js", "isDirectory": false, "importance": 10, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [{"name": "express-validator", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\express-validator", "version": "^7.2.1"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js", "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\models", "name": "models", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\models\\LearningRecord.js", "name": "LearningRecord.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose", "version": "^8.17.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "name": "ReviewSchedule.js", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose", "version": "^8.17.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "name": "Task.js", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose", "version": "^8.17.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "name": "User.js", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose", "version": "^8.17.0"}, {"name": "bcryptjs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\bcryptjs", "version": "^3.0.2"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes", "name": "routes", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\analytics.js", "name": "analytics.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js"], "packageDependencies": [{"name": "express", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\express", "version": "^5.1.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "name": "auth.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js"], "packageDependencies": [{"name": "express", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\express", "version": "^5.1.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\docs.js", "name": "docs.js", "isDirectory": false, "importance": 7, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\config\\swagger.js"], "packageDependencies": [{"name": "express", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\express", "version": "^5.1.0"}, {"name": "js-yaml", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\js-yaml", "version": "^4.1.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\reviews.js", "name": "reviews.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js"], "packageDependencies": [{"name": "express", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\express", "version": "^5.1.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\routes\\tasks.js", "name": "tasks.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\taskController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js"], "packageDependencies": [{"name": "express", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\express", "version": "^5.1.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\server.js", "name": "server.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js"], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\services", "name": "services", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "name": "authService.js", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\redis.js"], "packageDependencies": [{"name": "jsonwebtoken", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\jsonwebtoken", "version": "^9.0.2"}, {"name": "bcryptjs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\bcryptjs", "version": "^3.0.2"}, {"name": "crypto", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\crypto"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "name": "ebbinghausService.js", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.simple.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.test.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "name": "loadBalanceService.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "name": "taskService.js", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\LearningRecord.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "E:\\JYZS\\ebbinghaus-backend\\src\\config\\logger.js"], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\taskController.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\utils", "name": "utils", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "name": "constants.js", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\taskController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\validation.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\LearningRecord.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js"]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "name": "helpers.js", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "uuid", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\uuid", "version": "^11.1.0"}, {"name": "dayjs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\dayjs", "version": "^1.11.13"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\analyticsController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\controllers\\reviewController.js", "E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\auth.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\authService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\loadBalanceService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js"]}]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests", "name": "tests", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\fixtures", "name": "fixtures", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js", "name": "testData.js", "isDirectory": false, "importance": 6, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers", "name": "helpers", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\setup.js", "name": "setup.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "mongoose", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose", "version": "^8.17.0"}, {"name": "mongodb-memory-server", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongodb-memory-server", "version": "^10.2.0", "isDevDependency": true}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "name": "testUtils.js", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js"], "packageDependencies": [{"name": "jsonwebtoken", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\jsonwebtoken", "version": "^9.0.2"}, {"name": "bcryptjs", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\bcryptjs", "version": "^3.0.2"}, {"name": "mongoose", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose", "version": "^8.17.0"}], "dependents": ["E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.test.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\integration", "name": "integration", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "name": "auth.test.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\User.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js"], "packageDependencies": [{"name": "supertest", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\supertest", "version": "^7.1.4", "isDevDependency": true}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\health.test.js", "name": "health.test.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js"], "packageDependencies": [{"name": "supertest", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\supertest", "version": "^7.1.4", "isDevDependency": true}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js", "name": "tasks.test.js", "isDirectory": false, "importance": 6, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\app.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js"], "packageDependencies": [{"name": "supertest", "path": "E:\\JYZS\\ebbinghaus-backend\\node_modules\\supertest", "version": "^7.1.4", "isDevDependency": true}], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\unit", "name": "unit", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.simple.test.js", "name": "ebbinghausService.simple.test.js", "isDirectory": false, "importance": 4, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js"], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.test.js", "name": "ebbinghausService.test.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\services\\ebbinghausService.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js"], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js", "name": "taskService.test.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-backend\\src\\services\\taskService.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\Task.js", "E:\\JYZS\\ebbinghaus-backend\\src\\models\\ReviewSchedule.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "E:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js"], "packageDependencies": [], "dependents": []}]}]}, {"path": "E:\\JYZS\\ebbinghaus-backend\\tsconfig.json", "name": "tsconfig.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}]}}