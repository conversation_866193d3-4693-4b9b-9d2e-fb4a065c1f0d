graph TB
classDef package-node fill:#a29bfe,stroke:#2d3436,shape:ellipse
classDef package-scope-node fill:#ffeaa7,stroke:#2d3436,shape:stadium

  %% Package Scopes

  %% Node Definitions & Styles
  node0["ebbinghaus-backend"];
  style node0 fill:#74b9ff,stroke:#333,stroke-width:1px
  node1["coverage"];
  style node1 fill:#74b9ff,stroke:#333,stroke-width:1px
  node2["docs"];
  style node2 fill:#74b9ff,stroke:#333,stroke-width:1px
  node3["logs"];
  style node3 fill:#74b9ff,stroke:#333,stroke-width:1px
  node4["scripts"];
  style node4 fill:#74b9ff,stroke:#333,stroke-width:1px
  node5["src"];
  style node5 fill:#74b9ff,stroke:#333,stroke-width:1px
  node6["tests"];
  style node6 fill:#74b9ff,stroke:#333,stroke-width:1px
  node7["lcov-report"];
  style node7 fill:#74b9ff,stroke:#333,stroke-width:1px
  node8["src"];
  style node8 fill:#74b9ff,stroke:#333,stroke-width:1px
  node9["health-check.js"];
  style node9 fill:#74b9ff,stroke:#333,stroke-width:1px
  node10["start-server.js"];
  style node10 fill:#ff7675,stroke:#333,stroke-width:1px
  node11["troubleshoot.js"];
  style node11 fill:#74b9ff,stroke:#333,stroke-width:1px
  node12["dotenv v\^17.2.1"];
  style node12 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node12 package-node
  node13["http"];
  style node13 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node13 package-node
  node14["child_process"];
  style node14 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node14 package-node
  node15["util"];
  style node15 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node15 package-node
  node16["fs"];
  style node16 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node16 package-node
  node17["path"];
  style node17 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node17 package-node
  node18["mongoose v\^8.17.0"];
  style node18 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node18 package-node
  node19["redis v\^5.7.0"];
  style node19 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node19 package-node
  node20["app.js"];
  style node20 fill:#ff7675,stroke:#333,stroke-width:1px
  node21["config"];
  style node21 fill:#74b9ff,stroke:#333,stroke-width:1px
  node22["controllers"];
  style node22 fill:#74b9ff,stroke:#333,stroke-width:1px
  node23["middleware"];
  style node23 fill:#74b9ff,stroke:#333,stroke-width:1px
  node24["models"];
  style node24 fill:#74b9ff,stroke:#333,stroke-width:1px
  node25["routes"];
  style node25 fill:#74b9ff,stroke:#333,stroke-width:1px
  node26["server.js"];
  style node26 fill:#ff7675,stroke:#333,stroke-width:1px
  node27["services"];
  style node27 fill:#74b9ff,stroke:#333,stroke-width:1px
  node28["utils"];
  style node28 fill:#74b9ff,stroke:#333,stroke-width:1px
  node29["express v\^5.1.0"];
  style node29 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node29 package-node
  node30["cors v\^2.8.5"];
  style node30 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node30 package-node
  node31["helmet v\^8.1.0"];
  style node31 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node31 package-node
  node32["fixtures"];
  style node32 fill:#74b9ff,stroke:#333,stroke-width:1px
  node33["helpers"];
  style node33 fill:#74b9ff,stroke:#333,stroke-width:1px
  node34["integration"];
  style node34 fill:#74b9ff,stroke:#333,stroke-width:1px
  node35["unit"];
  style node35 fill:#74b9ff,stroke:#333,stroke-width:1px

  %% Edge Definitions
  node0 --> node1
  linkStyle 0 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node2
  linkStyle 1 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node3
  linkStyle 2 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node4
  linkStyle 3 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node5
  linkStyle 4 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node6
  linkStyle 5 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node7
  linkStyle 6 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node8
  linkStyle 7 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node9
  linkStyle 8 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node10
  linkStyle 9 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node11
  linkStyle 10 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node12
  linkStyle 11 stroke:#6c5ce7,stroke-width:1.5px
  node9 --> node13
  linkStyle 12 stroke:#6c5ce7,stroke-width:1.5px
  node9 --> node14
  linkStyle 13 stroke:#6c5ce7,stroke-width:1.5px
  node9 --> node15
  linkStyle 14 stroke:#6c5ce7,stroke-width:1.5px
  node10 --> node12
  linkStyle 15 stroke:#6c5ce7,stroke-width:1.5px
  node10 --> node14
  linkStyle 16 stroke:#6c5ce7,stroke-width:1.5px
  node10 --> node16
  linkStyle 17 stroke:#6c5ce7,stroke-width:1.5px
  node10 --> node17
  linkStyle 18 stroke:#6c5ce7,stroke-width:1.5px
  node10 --> node15
  linkStyle 19 stroke:#6c5ce7,stroke-width:1.5px
  node10 --> node18
  linkStyle 20 stroke:#6c5ce7,stroke-width:1.5px
  node10 --> node19
  linkStyle 21 stroke:#6c5ce7,stroke-width:1.5px
  node10 --> node13
  linkStyle 22 stroke:#6c5ce7,stroke-width:1.5px
  node11 --> node12
  linkStyle 23 stroke:#6c5ce7,stroke-width:1.5px
  node11 --> node14
  linkStyle 24 stroke:#6c5ce7,stroke-width:1.5px
  node11 --> node16
  linkStyle 25 stroke:#6c5ce7,stroke-width:1.5px
  node11 --> node17
  linkStyle 26 stroke:#6c5ce7,stroke-width:1.5px
  node11 --> node15
  linkStyle 27 stroke:#6c5ce7,stroke-width:1.5px
  node5 --> node20
  linkStyle 28 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node21
  linkStyle 29 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node22
  linkStyle 30 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node23
  linkStyle 31 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node24
  linkStyle 32 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node25
  linkStyle 33 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node26
  linkStyle 34 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node27
  linkStyle 35 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node28
  linkStyle 36 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node20 --> node12
  linkStyle 37 stroke:#6c5ce7,stroke-width:1.5px
  node20 --> node29
  linkStyle 38 stroke:#6c5ce7,stroke-width:1.5px
  node20 --> node30
  linkStyle 39 stroke:#6c5ce7,stroke-width:1.5px
  node20 --> node31
  linkStyle 40 stroke:#6c5ce7,stroke-width:1.5px
  node26 --> node20
  linkStyle 41 stroke:#636e72,stroke-width:1px
  node6 --> node32
  linkStyle 42 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node33
  linkStyle 43 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node34
  linkStyle 44 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node35
  linkStyle 45 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5