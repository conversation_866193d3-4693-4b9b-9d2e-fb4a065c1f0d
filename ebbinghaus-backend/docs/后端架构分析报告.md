# JYZS 艾宾浩斯学习系统后端架构分析报告

## 📋 项目概览

### 基本信息
- **项目名称**: JYZS 艾宾浩斯学习系统后端
- **技术栈**: Node.js + Express + MongoDB + Redis
- **架构模式**: MVC + 服务层架构
- **开发状态**: 生产就绪
- **API版本**: v1.0.0

### 核心技术栈详情

```json
{
  "运行时": "Node.js (>=18.0.0)",
  "框架": "Express 5.1.0",
  "数据库": "MongoDB (Mongoose 8.17.0)",
  "缓存": "Redis",
  "认证": "JWT (jsonwebtoken 9.0.2)",
  "加密": "bcryptjs 3.0.2",
  "日志": "Winston",
  "测试": "Jest + Supertest",
  "文档": "Swagger/OpenAPI",
  "监控": "自定义性能监控"
}
```

## 🏗️ 项目目录结构

```
ebbinghaus-backend/
├── src/
│   ├── app.js               # Express应用入口
│   ├── server.js            # 服务器启动文件
│   ├── config/              # 配置模块 (7个文件)
│   │   ├── database.js      # MongoDB配置
│   │   ├── redis.js         # Redis配置
│   │   ├── logger.js        # 日志配置
│   │   ├── environment.js   # 环境变量
│   │   ├── swagger.js       # API文档配置
│   │   └── performance.js   # 性能监控配置
│   ├── controllers/         # 控制器层 (4个控制器)
│   │   ├── authController.js
│   │   ├── taskController.js
│   │   ├── reviewController.js
│   │   └── analyticsController.js
│   ├── models/              # 数据模型 (4个模型)
│   │   ├── User.js
│   │   ├── Task.js
│   │   ├── ReviewSchedule.js
│   │   └── LearningRecord.js
│   ├── services/            # 服务层 (4个服务)
│   │   ├── authService.js
│   │   ├── taskService.js
│   │   ├── ebbinghausService.js
│   │   └── loadBalanceService.js
│   ├── middleware/          # 中间件 (4个中间件)
│   │   ├── auth.js
│   │   ├── errorHandler.js
│   │   ├── rateLimit.js
│   │   └── validation.js
│   ├── routes/              # 路由层 (5个路由)
│   │   ├── auth.js
│   │   ├── tasks.js
│   │   ├── reviews.js
│   │   ├── analytics.js
│   │   └── docs.js
│   └── utils/               # 工具函数 (2个工具)
│       ├── constants.js
│       └── helpers.js
├── tests/                   # 测试文件
├── docs/                    # 项目文档
└── 配置文件
```

## 🎯 核心功能模块

### 1. 用户认证模块 (Auth)
- **功能**: 用户注册、登录、JWT令牌管理
- **控制器**: authController.js
- **服务**: authService.js
- **模型**: User.js
- **路由**: /api/auth/*
- **特性**: 
  - JWT令牌认证
  - 密码加密 (bcryptjs)
  - 刷新令牌机制
  - 用户权限管理

### 2. 任务管理模块 (Tasks)
- **功能**: 学习任务的CRUD操作
- **控制器**: taskController.js
- **服务**: taskService.js
- **模型**: Task.js
- **路由**: /api/tasks/*
- **特性**:
  - 任务创建、编辑、删除
  - 任务状态管理
  - 任务搜索和筛选
  - 任务统计分析

### 3. 复习系统模块 (Reviews)
- **功能**: 基于艾宾浩斯遗忘曲线的复习调度
- **控制器**: reviewController.js
- **服务**: ebbinghausService.js
- **模型**: ReviewSchedule.js, LearningRecord.js
- **路由**: /api/reviews/*
- **特性**:
  - 智能复习计划生成
  - 复习效果评估
  - 个性化间隔调整
  - 复习提醒系统

### 4. 数据分析模块 (Analytics)
- **功能**: 学习数据统计和分析
- **控制器**: analyticsController.js
- **服务**: loadBalanceService.js
- **路由**: /api/analytics/*
- **特性**:
  - 学习进度统计
  - 性能分析报告
  - 趋势分析
  - 负载均衡优化

## 🔧 架构设计模式

### 1. MVC + 服务层架构
```mermaid
graph TD
    A[客户端请求] --> B[路由层 Routes]
    B --> C[中间件 Middleware]
    C --> D[控制器 Controllers]
    D --> E[服务层 Services]
    E --> F[数据模型 Models]
    F --> G[数据库 MongoDB]
    
    E --> H[外部服务]
    E --> I[缓存 Redis]
    
    style D fill:#ff9800
    style E fill:#4caf50
    style F fill:#2196f3
```

### 2. 请求处理流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant R as 路由
    participant M as 中间件
    participant Ctrl as 控制器
    participant S as 服务
    participant DB as 数据库

    C->>R: HTTP请求
    R->>M: 路由匹配
    M->>M: 认证验证
    M->>M: 参数验证
    M->>Ctrl: 调用控制器
    Ctrl->>S: 调用业务服务
    S->>DB: 数据操作
    DB->>S: 返回数据
    S->>Ctrl: 处理结果
    Ctrl->>C: JSON响应
```

### 3. 艾宾浩斯算法架构
```mermaid
graph LR
    A[任务完成] --> B[生成复习计划]
    B --> C[复习间隔计算]
    C --> D[复习提醒]
    D --> E[复习执行]
    E --> F[效果评估]
    F --> G[间隔调整]
    G --> C
    
    style B fill:#4caf50
    style F fill:#ff9800
    style G fill:#2196f3
```

## 📊 模块重要性分析

### 核心模块 (重要性 9-10)
1. **app.js** (10/10) - 应用入口和配置
2. **rateLimit.js** (10/10) - 限流中间件
3. **validation.js** (10/10) - 参数验证中间件
4. **User.js** (9/10) - 用户数据模型
5. **Task.js** (9/10) - 任务数据模型
6. **ReviewSchedule.js** (9/10) - 复习计划模型
7. **authService.js** (9/10) - 认证服务
8. **ebbinghausService.js** (9/10) - 艾宾浩斯算法服务
9. **auth.js** (9/10) - 认证中间件
10. **errorHandler.js** (9/10) - 错误处理中间件
11. **helpers.js** (9/10) - 工具函数

### 重要模块 (重要性 7-8)
12. **server.js** (8/10) - 服务器启动
13. **LearningRecord.js** (8/10) - 学习记录模型
14. **taskService.js** (8/10) - 任务服务
15. **loadBalanceService.js** (8/10) - 负载均衡服务
16. **所有路由文件** (8/10) - API路由定义
17. **所有控制器文件** (7/10) - 业务逻辑控制

### 支撑模块 (重要性 5-7)
18. **constants.js** (7/10) - 常量定义
19. **所有配置文件** (6-7/10) - 系统配置

## 🔍 数据模型设计

### 用户模型 (User)
```javascript
{
  userId: String,           // 用户唯一标识
  email: String,            // 邮箱 (唯一)
  password: String,         // 加密密码
  profile: {
    name: String,           // 用户姓名
    avatar: String,         // 头像URL
    preferences: Object     // 用户偏好设置
  },
  security: {
    lastLogin: Date,        // 最后登录时间
    loginAttempts: Number,  // 登录尝试次数
    lockUntil: Date        // 账户锁定时间
  },
  timestamps: true          // 创建和更新时间
}
```

### 任务模型 (Task)
```javascript
{
  taskId: String,           // 任务唯一标识
  userId: ObjectId,         // 用户ID (外键)
  title: String,            // 任务标题
  content: {
    text: String,           // 文本内容
    images: [String],       // 图片URL数组
    audio: String,          // 音频URL
    attachments: [String]   // 附件URL数组
  },
  metadata: {
    subject: String,        // 学科分类
    estimatedTime: Number,  // 预估时间(分钟)
    actualTime: Number,     // 实际时间(分钟)
    priority: Number,       // 优先级 (1-5)
    difficulty: Number,     // 难度级别 (1-5)
    tags: [String]          // 标签数组
  },
  status: String,           // 任务状态
  progress: {
    completedAt: Date,      // 完成时间
    startedAt: Date,        // 开始时间
    notes: String           // 备注
  },
  searchText: String,       // 搜索文本索引
  mindMapId: String,        // 关联思维导图ID
  parentTaskId: ObjectId    // 父任务ID
}
```

### 复习计划模型 (ReviewSchedule)
```javascript
{
  scheduleId: String,       // 计划唯一标识
  taskId: ObjectId,         // 关联任务ID
  userId: ObjectId,         // 用户ID
  reviewIndex: Number,      // 复习轮次 (1-7)
  intervalDays: Number,     // 间隔天数
  scheduledTime: Date,      // 计划复习时间
  actualTime: Date,         // 实际复习时间
  status: String,           // 复习状态
  effectiveness: Number,    // 复习效果评分 (1-5)
  notes: String,            // 复习笔记
  adjustmentFactor: Number  // 间隔调整系数
}
```

### 学习记录模型 (LearningRecord)
```javascript
{
  recordId: String,         // 记录唯一标识
  userId: ObjectId,         // 用户ID
  taskId: ObjectId,         // 任务ID
  scheduleId: ObjectId,     // 复习计划ID (可选)
  type: String,             // 记录类型 (task/review)
  startTime: Date,          // 开始时间
  endTime: Date,            // 结束时间
  duration: Number,         // 持续时间(分钟)
  effectiveness: Number,    // 效果评分
  notes: String,            // 学习笔记
  metadata: Object          // 扩展元数据
}
```

## 🚀 核心服务详解

### 1. 艾宾浩斯服务 (EbbinghausService)
```javascript
// 核心功能
- generateReviewSchedule()    // 生成复习计划
- adjustReviewSchedule()      // 调整复习间隔
- calculateNextReviewTime()   // 计算下次复习时间
- personalizeIntervals()      // 个性化间隔调整
- calculateUrgency()          // 计算复习紧急程度
- getReviewRecommendation()   // 获取复习建议

// 算法参数
intervals: [1, 10, 60, 360, 1440, 4320, 10080] // 分钟
effectivenessFactors: {
  1: 0.6,  // 很差 - 缩短间隔
  2: 0.8,  // 较差
  3: 1.0,  // 一般 - 保持间隔
  4: 1.3,  // 较好
  5: 1.6   // 很好 - 延长间隔
}
```

### 2. 任务服务 (TaskService)
```javascript
// 核心功能
- createTask()               // 创建任务
- getTask()                  // 获取任务详情
- getUserTasks()             // 获取用户任务列表
- updateTask()               // 更新任务
- updateTaskStatus()         // 更新任务状态
- deleteTask()               // 删除任务
- searchTasks()              // 搜索任务
- getTaskStatistics()        // 获取任务统计
- getTodayTasks()            // 获取今日任务
```

### 3. 认证服务 (AuthService)
```javascript
// 核心功能
- generateToken()            // 生成JWT令牌
- verifyToken()              // 验证JWT令牌
- hashPassword()             // 密码加密
- comparePassword()          // 密码比较
- generateRefreshToken()     // 生成刷新令牌
- revokeToken()              // 撤销令牌
```

## 🛡️ 安全架构设计

### 1. 认证授权机制
```mermaid
graph TD
    A[用户登录] --> B[验证凭据]
    B --> C[生成JWT令牌]
    C --> D[返回令牌]
    D --> E[请求携带令牌]
    E --> F[中间件验证]
    F --> G[解析用户信息]
    G --> H[授权访问]
    
    style C fill:#4caf50
    style F fill:#ff9800
```

### 2. 安全措施
- **JWT令牌**: 无状态认证，支持刷新机制
- **密码加密**: bcryptjs加密存储
- **限流保护**: express-rate-limit防止暴力攻击
- **参数验证**: express-validator输入验证
- **CORS配置**: 跨域请求控制
- **Helmet安全**: HTTP头部安全设置
- **错误处理**: 统一错误处理，避免信息泄露

### 3. 数据验证
```javascript
// 输入验证示例
const taskValidation = [
  body('title').isLength({ min: 1, max: 100 }).trim(),
  body('content.text').isLength({ min: 1, max: 5000 }),
  body('metadata.subject').isIn(Object.values(SUBJECTS)),
  body('metadata.estimatedTime').isInt({ min: 1, max: 300 }),
  body('metadata.priority').isInt({ min: 1, max: 5 }),
  body('metadata.difficulty').isInt({ min: 1, max: 5 })
];
```

---

**文档版本**: v1.0  
**生成时间**: 2025-08-03  
**分析工具**: FileScopeMCP  
**项目状态**: 生产就绪
