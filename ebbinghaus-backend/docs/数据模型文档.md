# JYZS 后端数据模型文档

## 📋 数据模型概览

### 基本信息
- **数据库**: MongoDB 8.17.0
- **ODM框架**: Mongoose
- **模型数量**: 4个核心模型
- **设计模式**: 文档型数据库设计
- **索引策略**: 复合索引 + 文本索引

### 模型统计
- **用户模型**: User.js (重要性: 9/10)
- **任务模型**: Task.js (重要性: 9/10)
- **复习计划模型**: ReviewSchedule.js (重要性: 9/10)
- **学习记录模型**: LearningRecord.js (重要性: 8/10)

## 🗄️ 数据模型关系图

### 整体关系架构
```mermaid
erDiagram
    User ||--o{ Task : creates
    User ||--o{ ReviewSchedule : owns
    User ||--o{ LearningRecord : generates
    Task ||--o{ ReviewSchedule : triggers
    Task ||--o{ LearningRecord : records
    ReviewSchedule ||--o{ LearningRecord : produces
    Task ||--o{ Task : parent-child

    User {
        ObjectId _id
        String userId
        String email
        String password
        Object profile
        Object security
        Date createdAt
        Date updatedAt
    }

    Task {
        ObjectId _id
        String taskId
        ObjectId userId
        String title
        Object content
        Object metadata
        String status
        Object progress
        String searchText
        String mindMapId
        ObjectId parentTaskId
        Date createdAt
        Date updatedAt
    }

    ReviewSchedule {
        ObjectId _id
        String scheduleId
        ObjectId taskId
        ObjectId userId
        Number reviewIndex
        Number intervalDays
        Date scheduledTime
        Date actualTime
        String status
        Number effectiveness
        String notes
        Number adjustmentFactor
        Date createdAt
        Date updatedAt
    }

    LearningRecord {
        ObjectId _id
        String recordId
        ObjectId userId
        ObjectId taskId
        ObjectId scheduleId
        String type
        Date startTime
        Date endTime
        Number duration
        Number effectiveness
        String notes
        Object metadata
        Date createdAt
        Date updatedAt
    }
```

## 👤 用户模型 (User)

### 模型结构
```javascript
const userSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    index: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  profile: {
    name: {
      type: String,
      required: true,
      maxlength: 50,
      trim: true
    },
    avatar: {
      type: String,
      default: null
    },
    bio: {
      type: String,
      maxlength: 200
    },
    preferences: {
      language: {
        type: String,
        enum: ['zh-CN', 'en-US'],
        default: 'zh-CN'
      },
      timezone: {
        type: String,
        default: 'Asia/Shanghai'
      },
      notifications: {
        email: { type: Boolean, default: true },
        push: { type: Boolean, default: true },
        review: { type: Boolean, default: true }
      },
      study: {
        dailyGoal: { type: Number, default: 60 }, // 分钟
        preferredTime: { type: String, default: 'morning' },
        difficulty: { type: Number, default: 3 }
      }
    }
  },
  security: {
    lastLogin: Date,
    loginAttempts: {
      type: Number,
      default: 0
    },
    lockUntil: Date,
    passwordChangedAt: Date,
    emailVerified: {
      type: Boolean,
      default: false
    },
    emailVerificationToken: String,
    passwordResetToken: String,
    passwordResetExpires: Date
  },
  statistics: {
    totalTasks: { type: Number, default: 0 },
    completedTasks: { type: Number, default: 0 },
    totalStudyTime: { type: Number, default: 0 }, // 分钟
    averageEffectiveness: { type: Number, default: 3 },
    streakDays: { type: Number, default: 0 },
    lastActiveDate: Date
  }
}, {
  timestamps: true,
  collection: 'users'
});
```

### 索引策略
```javascript
// 复合索引
userSchema.index({ email: 1, emailVerified: 1 });
userSchema.index({ userId: 1, 'security.lastLogin': -1 });

// 文本索引
userSchema.index({ 
  'profile.name': 'text',
  'profile.bio': 'text'
});
```

### 实例方法
```javascript
// 密码验证
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// 生成JWT令牌
userSchema.methods.generateAuthToken = function() {
  return jwt.sign(
    { userId: this.userId, email: this.email },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN }
  );
};

// 更新统计信息
userSchema.methods.updateStatistics = function(stats) {
  Object.assign(this.statistics, stats);
  return this.save();
};
```

## 📝 任务模型 (Task)

### 模型结构
```javascript
const taskSchema = new mongoose.Schema({
  taskId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  title: {
    type: String,
    required: true,
    maxlength: 100,
    trim: true
  },
  content: {
    text: {
      type: String,
      required: true,
      maxlength: 5000
    },
    images: [{
      type: String // 图片URL
    }],
    audio: {
      type: String // 音频URL
    },
    attachments: [{
      type: String // 附件URL
    }]
  },
  metadata: {
    subject: {
      type: String,
      required: true,
      enum: ['mathematics', 'physics', 'chemistry', 'biology', 
             'history', 'geography', 'literature', 'english', 
             'programming', 'other']
    },
    estimatedTime: {
      type: Number,
      required: true,
      min: 1,
      max: 300 // 最大5小时
    },
    actualTime: {
      type: Number,
      min: 0
    },
    priority: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
      default: 3
    },
    difficulty: {
      type: Number,
      required: true,
      min: 1,
      max: 5,
      default: 3
    },
    tags: [{
      type: String,
      trim: true
    }]
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'cancelled'],
    default: 'pending',
    index: true
  },
  progress: {
    completedAt: Date,
    startedAt: Date,
    notes: {
      type: String,
      maxlength: 1000
    }
  },
  searchText: {
    type: String,
    index: 'text'
  },
  mindMapId: {
    type: String,
    index: true
  },
  parentTaskId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Task',
    index: true
  }
}, {
  timestamps: true,
  collection: 'tasks'
});
```

### 索引策略
```javascript
// 复合索引
taskSchema.index({ userId: 1, status: 1, createdAt: -1 });
taskSchema.index({ userId: 1, 'metadata.subject': 1, createdAt: -1 });
taskSchema.index({ userId: 1, 'metadata.priority': 1, createdAt: -1 });

// 文本搜索索引
taskSchema.index({ 
  title: 'text',
  'content.text': 'text',
  'metadata.tags': 'text'
});
```

### 静态方法
```javascript
// 获取用户任务统计
taskSchema.statics.getUserTaskStats = async function(userId) {
  return this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalEstimatedTime: { $sum: '$metadata.estimatedTime' },
        totalActualTime: { $sum: '$metadata.actualTime' }
      }
    }
  ]);
};

// 搜索任务
taskSchema.statics.searchTasks = function(userId, searchQuery, options = {}) {
  const query = {
    userId,
    $text: { $search: searchQuery }
  };
  
  if (options.status) query.status = options.status;
  if (options.subject) query['metadata.subject'] = options.subject;
  
  return this.find(query, { score: { $meta: 'textScore' } })
    .sort({ score: { $meta: 'textScore' } });
};
```

## 🔄 复习计划模型 (ReviewSchedule)

### 模型结构
```javascript
const reviewScheduleSchema = new mongoose.Schema({
  scheduleId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  taskId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Task',
    required: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  reviewIndex: {
    type: Number,
    required: true,
    min: 1,
    max: 7 // 艾宾浩斯7个复习点
  },
  intervalDays: {
    type: Number,
    required: true,
    min: 0
  },
  scheduledTime: {
    type: Date,
    required: true,
    index: true
  },
  actualTime: {
    type: Date,
    index: true
  },
  status: {
    type: String,
    enum: ['scheduled', 'completed', 'skipped', 'overdue'],
    default: 'scheduled',
    index: true
  },
  effectiveness: {
    type: Number,
    min: 1,
    max: 5,
    validate: {
      validator: function(v) {
        return this.status === 'completed' ? v != null : true;
      },
      message: 'Effectiveness is required when status is completed'
    }
  },
  notes: {
    type: String,
    maxlength: 500
  },
  adjustmentFactor: {
    type: Number,
    default: 1.0,
    min: 0.1,
    max: 3.0
  }
}, {
  timestamps: true,
  collection: 'review_schedules'
});
```

### 索引策略
```javascript
// 复合索引
reviewScheduleSchema.index({ userId: 1, status: 1, scheduledTime: 1 });
reviewScheduleSchema.index({ taskId: 1, reviewIndex: 1 });
reviewScheduleSchema.index({ userId: 1, scheduledTime: 1, status: 1 });

// 时间范围查询索引
reviewScheduleSchema.index({ scheduledTime: 1, status: 1 });
```

### 虚拟字段
```javascript
// 是否逾期
reviewScheduleSchema.virtual('isOverdue').get(function() {
  return this.status === 'scheduled' && 
         this.scheduledTime < new Date();
});

// 紧急程度
reviewScheduleSchema.virtual('urgency').get(function() {
  if (this.status !== 'scheduled') return 'none';
  
  const now = new Date();
  const hoursDiff = (this.scheduledTime - now) / (1000 * 60 * 60);
  
  if (hoursDiff < 0) return 'overdue';
  if (hoursDiff < 2) return 'urgent';
  if (hoursDiff < 24) return 'soon';
  return 'normal';
});
```

## 📊 学习记录模型 (LearningRecord)

### 模型结构
```javascript
const learningRecordSchema = new mongoose.Schema({
  recordId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  taskId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Task',
    required: true,
    index: true
  },
  scheduleId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ReviewSchedule',
    index: true
  },
  type: {
    type: String,
    enum: ['task', 'review'],
    required: true,
    index: true
  },
  startTime: {
    type: Date,
    required: true,
    index: true
  },
  endTime: {
    type: Date,
    required: true
  },
  duration: {
    type: Number,
    required: true,
    min: 1 // 至少1分钟
  },
  effectiveness: {
    type: Number,
    required: true,
    min: 1,
    max: 5
  },
  notes: {
    type: String,
    maxlength: 1000
  },
  metadata: {
    environment: {
      type: String,
      enum: ['quiet', 'noisy', 'normal'],
      default: 'normal'
    },
    mood: {
      type: String,
      enum: ['excellent', 'good', 'normal', 'poor', 'terrible'],
      default: 'normal'
    },
    interruptions: {
      type: Number,
      default: 0,
      min: 0
    },
    tools: [{
      type: String // 使用的学习工具
    }]
  }
}, {
  timestamps: true,
  collection: 'learning_records'
});
```

### 索引策略
```javascript
// 复合索引
learningRecordSchema.index({ userId: 1, type: 1, startTime: -1 });
learningRecordSchema.index({ taskId: 1, type: 1, startTime: -1 });
learningRecordSchema.index({ userId: 1, startTime: -1, endTime: -1 });

// 时间范围查询索引
learningRecordSchema.index({ startTime: 1, endTime: 1 });
```

### 静态方法
```javascript
// 获取用户学习统计
learningRecordSchema.statics.getUserLearningStats = async function(userId, dateRange) {
  const matchStage = { userId: new mongoose.Types.ObjectId(userId) };
  
  if (dateRange) {
    matchStage.startTime = {
      $gte: dateRange.start,
      $lte: dateRange.end
    };
  }
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalSessions: { $sum: 1 },
        totalDuration: { $sum: '$duration' },
        averageEffectiveness: { $avg: '$effectiveness' },
        averageDuration: { $avg: '$duration' }
      }
    }
  ]);
};
```

## 🔧 数据模型最佳实践

### 1. 数据验证
```javascript
// 自定义验证器
const emailValidator = {
  validator: function(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  },
  message: 'Invalid email format'
};

// 条件验证
const conditionalRequired = {
  validator: function(value) {
    return this.status === 'completed' ? value != null : true;
  },
  message: 'Field is required when status is completed'
};
```

### 2. 中间件钩子
```javascript
// 保存前处理
taskSchema.pre('save', function(next) {
  // 更新搜索文本
  this.searchText = `${this.title} ${this.content.text}`.toLowerCase();
  
  // 自动设置完成时间
  if (this.status === 'completed' && !this.progress.completedAt) {
    this.progress.completedAt = new Date();
  }
  
  next();
});

// 删除后清理
taskSchema.post('remove', async function(doc) {
  // 删除相关的复习计划
  await ReviewSchedule.deleteMany({ taskId: doc._id });
  
  // 删除相关的学习记录
  await LearningRecord.deleteMany({ taskId: doc._id });
});
```

### 3. 性能优化
```javascript
// 分页查询优化
const getPaginatedTasks = async (userId, page, limit) => {
  const skip = (page - 1) * limit;
  
  return Task.find({ userId })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .lean(); // 返回普通对象，提高性能
};

// 聚合查询优化
const getTaskStatsBySubject = async (userId) => {
  return Task.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId) } },
    { $group: {
        _id: '$metadata.subject',
        count: { $sum: 1 },
        avgDifficulty: { $avg: '$metadata.difficulty' }
      }
    },
    { $sort: { count: -1 } }
  ]);
};
```

---

**文档版本**: v1.0  
**数据库版本**: MongoDB 8.17.0  
**ODM版本**: Mongoose 8.17.0  
**模型数量**: 4个核心模型
