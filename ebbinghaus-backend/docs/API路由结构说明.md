# JYZS 后端API路由结构说明

## 📋 API概览

### 基本信息
- **API版本**: v1.0
- **基础URL**: http://localhost:3002
- **认证方式**: JWT Bearer Token
- **响应格式**: JSON
- **字符编码**: UTF-8

### 路由统计
- **路由文件数**: 5个
- **主要API模块**: 4个 (auth, tasks, reviews, analytics)
- **辅助路由**: 1个 (docs)
- **中间件集成**: 认证、验证、限流、错误处理

## 🗺️ 路由架构图

### 整体路由结构
```mermaid
graph TD
    A[Express App] --> B[健康检查 /health]
    A --> C[根路径 /]
    A --> D[认证路由 /api/auth]
    A --> E[任务路由 /api/tasks]
    A --> F[复习路由 /api/reviews]
    A --> G[分析路由 /api/analytics]
    A --> H[文档路由 /api/docs]
    
    D --> I[auth.js]
    E --> J[tasks.js]
    F --> K[reviews.js]
    G --> L[analytics.js]
    H --> M[docs.js]
    
    style A fill:#ff6b6b
    style D fill:#4ecdc4
    style E fill:#45b7d1
    style F fill:#96ceb4
    style G fill:#feca57
```

### 中间件应用层次
```mermaid
graph LR
    A[请求] --> B[全局中间件]
    B --> C[路由级中间件]
    C --> D[控制器方法]
    
    B --> E[CORS]
    B --> F[Helmet安全]
    B --> G[JSON解析]
    B --> H[请求日志]
    
    C --> I[认证验证]
    C --> J[参数验证]
    C --> K[限流控制]
    C --> L[错误处理]
    
    style B fill:#4caf50
    style C fill:#ff9800
    style D fill:#2196f3
```

## 🔐 认证路由 (/api/auth)

### 路由列表
```javascript
POST   /api/auth/register        // 用户注册
POST   /api/auth/login           // 用户登录
POST   /api/auth/logout          // 用户登出
POST   /api/auth/refresh         // 刷新令牌
GET    /api/auth/profile         // 获取用户资料
PUT    /api/auth/profile         // 更新用户资料
POST   /api/auth/change-password // 修改密码
POST   /api/auth/forgot-password // 忘记密码
POST   /api/auth/reset-password  // 重置密码
```

### 中间件配置
```javascript
// 认证路由中间件栈
├── express.Router()
├── rateLimit (登录限流)
├── validation (参数验证)
├── auth (部分路由需要认证)
└── errorHandler (错误处理)
```

### 详细端点说明

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "name": "用户姓名"
}

Response:
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "userId": "uuid",
    "email": "<EMAIL>",
    "name": "用户姓名"
  }
}
```

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123"
}

Response:
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": { ... },
    "token": "jwt_token",
    "refreshToken": "refresh_token",
    "expiresIn": 3600
  }
}
```

## 📝 任务路由 (/api/tasks)

### 路由列表
```javascript
GET    /api/tasks               // 获取任务列表
POST   /api/tasks               // 创建新任务
GET    /api/tasks/:taskId       // 获取任务详情
PUT    /api/tasks/:taskId       // 更新任务
DELETE /api/tasks/:taskId       // 删除任务
PATCH  /api/tasks/:taskId/status // 更新任务状态
GET    /api/tasks/search        // 搜索任务
GET    /api/tasks/statistics    // 获取任务统计
GET    /api/tasks/today         // 获取今日任务
```

### 中间件配置
```javascript
// 任务路由中间件栈
├── express.Router()
├── auth (所有路由需要认证)
├── validation (参数验证)
├── rateLimit (创建任务限流)
└── errorHandler (错误处理)
```

### 详细端点说明

#### 创建任务
```http
POST /api/tasks
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "title": "学习Vue.js基础",
  "content": {
    "text": "学习Vue.js的基本概念和语法",
    "images": [],
    "audio": null,
    "attachments": []
  },
  "metadata": {
    "subject": "programming",
    "estimatedTime": 120,
    "priority": 3,
    "difficulty": 2,
    "tags": ["vue", "javascript", "frontend"]
  }
}

Response:
{
  "success": true,
  "message": "Task created successfully",
  "data": {
    "taskId": "uuid",
    "title": "学习Vue.js基础",
    "status": "pending",
    "createdAt": "2025-08-03T10:00:00Z"
  }
}
```

#### 获取任务列表
```http
GET /api/tasks?page=1&limit=20&status=pending&subject=programming
Authorization: Bearer jwt_token

Response:
{
  "success": true,
  "data": [
    {
      "taskId": "uuid",
      "title": "学习Vue.js基础",
      "status": "pending",
      "metadata": { ... },
      "createdAt": "2025-08-03T10:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "pages": 3
  }
}
```

## 🔄 复习路由 (/api/reviews)

### 路由列表
```javascript
GET    /api/reviews             // 获取复习计划列表
POST   /api/reviews             // 创建复习计划
GET    /api/reviews/:scheduleId // 获取复习计划详情
PUT    /api/reviews/:scheduleId // 更新复习计划
DELETE /api/reviews/:scheduleId // 删除复习计划
POST   /api/reviews/:scheduleId/complete // 完成复习
GET    /api/reviews/today       // 获取今日复习
GET    /api/reviews/upcoming    // 获取即将到来的复习
GET    /api/reviews/overdue     // 获取逾期复习
GET    /api/reviews/statistics  // 获取复习统计
```

### 中间件配置
```javascript
// 复习路由中间件栈
├── express.Router()
├── auth (所有路由需要认证)
├── validation (参数验证)
└── errorHandler (错误处理)
```

### 详细端点说明

#### 完成复习
```http
POST /api/reviews/:scheduleId/complete
Authorization: Bearer jwt_token
Content-Type: application/json

{
  "effectiveness": 4,
  "notes": "复习效果很好，理解更深入了",
  "actualTime": 25
}

Response:
{
  "success": true,
  "message": "Review completed successfully",
  "data": {
    "scheduleId": "uuid",
    "status": "completed",
    "effectiveness": 4,
    "nextReviewTime": "2025-08-04T10:00:00Z"
  }
}
```

#### 获取今日复习
```http
GET /api/reviews/today
Authorization: Bearer jwt_token

Response:
{
  "success": true,
  "data": {
    "scheduled": [
      {
        "scheduleId": "uuid",
        "taskId": "uuid",
        "taskTitle": "学习Vue.js基础",
        "reviewIndex": 2,
        "scheduledTime": "2025-08-03T14:00:00Z",
        "urgency": "soon"
      }
    ],
    "overdue": [],
    "completed": [],
    "statistics": {
      "totalScheduled": 5,
      "completed": 2,
      "remaining": 3
    }
  }
}
```

## 📊 分析路由 (/api/analytics)

### 路由列表
```javascript
GET    /api/analytics/overview     // 获取总览数据
GET    /api/analytics/tasks        // 任务分析数据
GET    /api/analytics/reviews      // 复习分析数据
GET    /api/analytics/performance  // 性能分析数据
GET    /api/analytics/trends       // 趋势分析数据
GET    /api/analytics/subjects     // 学科分析数据
GET    /api/analytics/time         // 时间分析数据
GET    /api/analytics/efficiency   // 效率分析数据
```

### 中间件配置
```javascript
// 分析路由中间件栈
├── express.Router()
├── auth (所有路由需要认证)
├── validation (参数验证)
└── errorHandler (错误处理)
```

### 详细端点说明

#### 获取总览数据
```http
GET /api/analytics/overview?period=7d
Authorization: Bearer jwt_token

Response:
{
  "success": true,
  "data": {
    "tasks": {
      "total": 50,
      "completed": 30,
      "pending": 15,
      "inProgress": 5
    },
    "reviews": {
      "total": 120,
      "completed": 100,
      "scheduled": 20,
      "overdue": 5
    },
    "time": {
      "totalStudyTime": 1800,
      "averageSessionTime": 45,
      "dailyAverage": 257
    },
    "performance": {
      "averageEffectiveness": 3.8,
      "completionRate": 85,
      "retentionRate": 78
    }
  }
}
```

## 📚 文档路由 (/api/docs)

### 路由列表
```javascript
GET    /api/docs               // API文档首页
GET    /api/docs/swagger.json  // Swagger JSON
GET    /api/docs/openapi.yaml  // OpenAPI YAML
```

### 功能说明
- **Swagger UI**: 交互式API文档界面
- **OpenAPI规范**: 标准化API描述
- **实时更新**: 与代码同步的文档

## 🛡️ 安全和中间件

### 全局中间件
```javascript
// 安全中间件
app.use(helmet())           // 安全头部设置
app.use(cors())             // 跨域资源共享
app.use(express.json())     // JSON解析
app.use(requestId)          // 请求ID生成
app.use(responseTime)       // 响应时间监控
```

### 路由级中间件
```javascript
// 认证中间件
const auth = require('../middleware/auth');

// 参数验证中间件
const { validate } = require('../middleware/validation');

// 限流中间件
const { createTaskLimit } = require('../middleware/rateLimit');

// 使用示例
router.post('/tasks', 
  auth,                    // 认证验证
  createTaskLimit,         // 创建任务限流
  validate.createTask,     // 参数验证
  taskController.createTask // 控制器方法
);
```

### 错误处理
```javascript
// 统一错误响应格式
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE",
  "details": { ... },      // 详细错误信息 (开发环境)
  "timestamp": "2025-08-03T10:00:00Z",
  "requestId": "uuid"
}
```

## 📈 性能优化

### 缓存策略
```javascript
// Redis缓存中间件
const cache = require('../middleware/cache');

router.get('/tasks/statistics',
  auth,
  cache('user_stats', 300), // 5分钟缓存
  analyticsController.getTaskStatistics
);
```

### 分页优化
```javascript
// 分页参数标准化
const pagination = {
  page: parseInt(req.query.page) || 1,
  limit: Math.min(parseInt(req.query.limit) || 20, 100),
  offset: (page - 1) * limit
};
```

### 响应压缩
```javascript
// 启用gzip压缩
app.use(compression({
  filter: (req, res) => {
    return compression.filter(req, res);
  },
  threshold: 1024
}));
```

## 🔍 监控和日志

### 请求日志
```javascript
// Morgan HTTP日志
app.use(morgan('combined', {
  stream: logger.stream,
  skip: (req, res) => res.statusCode < 400
}));
```

### 性能监控
```javascript
// 响应时间监控
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info('Request completed', {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration
    });
  });
  next();
});
```

---

**文档版本**: v1.0  
**API版本**: v1.0  
**更新时间**: 2025-08-03  
**路由总数**: 35+ 个端点
