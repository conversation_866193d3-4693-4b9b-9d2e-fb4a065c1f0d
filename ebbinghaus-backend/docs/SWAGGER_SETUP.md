# Swagger API 文档配置指南

## 📋 概述

本文档说明了艾宾浩斯学习系统的Swagger API文档配置和使用方法。

## 🚀 快速开始

### 启动应用并访问文档

```bash
# 启动应用
npm start

# 访问API文档
# Swagger UI: http://localhost:3002/api/docs
# OpenAPI JSON: http://localhost:3002/api/docs/json
# OpenAPI YAML: http://localhost:3002/api/docs/yaml
```

### 验证文档配置

```bash
# 验证Swagger配置
npm run docs:validate

# 生成OpenAPI规范文件
npm run docs:generate

# 启动独立文档服务器
npm run docs:serve
```

## 📁 文件结构

```
ebbinghaus-backend/
├── src/
│   ├── config/
│   │   └── swagger.js          # Swagger配置文件
│   └── routes/
│       ├── auth.js             # 认证路由（含Swagger注释）
│       ├── tasks.js            # 任务路由（含Swagger注释）
│       ├── reviews.js          # 复习路由（含Swagger注释）
│       ├── analytics.js        # 分析路由
│       └── docs.js             # 文档路由
├── docs/
│   ├── API.md                  # API文档说明
│   ├── SWAGGER_SETUP.md        # 本文档
│   └── openapi.json            # 生成的OpenAPI规范
├── scripts/
│   └── validate-swagger.js     # 文档验证脚本
└── test-swagger.js             # 独立文档服务器
```

## ⚙️ 配置详解

### Swagger配置 (src/config/swagger.js)

主要配置项：

- **OpenAPI版本**: 3.0.0
- **API信息**: 标题、版本、描述、联系方式
- **服务器配置**: 开发、测试、生产环境
- **安全方案**: JWT Bearer认证
- **数据模型**: User、Task、ReviewSchedule等
- **响应模型**: 成功、错误、分页响应

### 路由注释格式

```javascript
/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: 用户登录
 *     description: 用户身份验证并获取访问令牌
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: 登录成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 */
```

## 📊 当前文档状态

根据最新验证结果：

- ✅ **Swagger配置**: 正常
- ✅ **依赖包**: 已安装
- ✅ **文档文件**: 完整
- ⚠️ **路由文档化率**: 25.0% (需要改进)

### 已文档化的路由

- **认证模块**: 部分路由已添加Swagger注释
- **任务模块**: 部分路由已添加Swagger注释
- **复习模块**: 部分路由已添加Swagger注释
- **文档模块**: 完整的Swagger注释

### 待完善的路由

- 分析模块的所有路由
- 认证模块的剩余路由
- 任务模块的剩余路由
- 复习模块的剩余路由

## 🔧 使用指南

### 1. 查看API文档

访问 `http://localhost:3001/api/docs` 查看交互式API文档。

### 2. 测试API接口

在Swagger UI中可以直接测试API接口：

1. 点击接口展开详情
2. 点击 "Try it out" 按钮
3. 填写请求参数
4. 点击 "Execute" 执行请求
5. 查看响应结果

### 3. 认证设置

对于需要认证的接口：

1. 点击页面右上角的 "Authorize" 按钮
2. 输入JWT令牌：`Bearer YOUR_JWT_TOKEN`
3. 点击 "Authorize" 确认
4. 现在可以测试需要认证的接口

### 4. 导出API规范

```bash
# 生成JSON格式
curl http://localhost:3001/api/docs/json > api-spec.json

# 生成YAML格式
curl http://localhost:3001/api/docs/yaml > api-spec.yaml

# 使用npm脚本生成
npm run docs:generate
```

## 📝 添加新的API文档

### 1. 为新路由添加Swagger注释

```javascript
/**
 * @swagger
 * /api/your-endpoint:
 *   post:
 *     summary: 接口描述
 *     tags: [YourModule]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               field1:
 *                 type: string
 *                 example: value1
 *     responses:
 *       200:
 *         description: 成功响应
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 */
```

### 2. 定义新的数据模型

在 `src/config/swagger.js` 的 `components.schemas` 中添加：

```javascript
YourModel: {
  type: 'object',
  properties: {
    id: {
      type: 'string',
      description: 'ID',
      example: '507f1f77bcf86cd799439011'
    },
    name: {
      type: 'string',
      description: '名称',
      example: '示例名称'
    }
  }
}
```

### 3. 验证文档

```bash
npm run docs:validate
```

## 🔍 故障排除

### 常见问题

1. **Swagger UI无法加载**
   - 检查应用是否正常启动
   - 确认路由配置正确
   - 查看控制台错误信息

2. **API接口不显示**
   - 检查Swagger注释格式
   - 确认路由文件路径正确
   - 验证YAML语法

3. **认证测试失败**
   - 确认JWT令牌格式正确
   - 检查令牌是否过期
   - 验证认证中间件配置

### 调试方法

```bash
# 检查Swagger配置
node -e "console.log(JSON.stringify(require('./src/config/swagger').swaggerSpec, null, 2))"

# 验证路由文件语法
node -c src/routes/your-route.js

# 启动独立文档服务器
npm run docs:serve
```

## 📈 改进建议

### 短期目标

1. **提高文档化率**: 为所有路由添加Swagger注释
2. **完善数据模型**: 添加缺失的Schema定义
3. **增加示例**: 为每个接口提供完整的请求/响应示例

### 长期目标

1. **自动化测试**: 基于Swagger规范生成API测试
2. **客户端生成**: 使用OpenAPI生成客户端SDK
3. **文档版本管理**: 建立API版本管理机制

## 📞 技术支持

如有问题，请：

1. 运行 `npm run docs:validate` 检查配置
2. 查看 `docs/swagger-validation-report.json` 验证报告
3. 检查应用日志和控制台错误
4. 参考 [Swagger官方文档](https://swagger.io/docs/)

---

**最后更新**: 2025-08-03  
**文档版本**: v1.0.0
