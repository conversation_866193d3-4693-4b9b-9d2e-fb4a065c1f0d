2025-08-03 14:20:34 [ERROR]: uncaughtException: Cannot find module 'rate-limit-redis'
Require stack:
- E:\JYZS\ebbinghaus-backend\src\middleware\rateLimit.js
- E:\JYZS\ebbinghaus-backend\[eval]
node:internal/modules/cjs/loader:1228
  throw err;
  ^

Error: Cannot find module 'rate-limit-redis'
Require stack:
- E:\JYZS\ebbinghaus-backend\src\middleware\rateLimit.js
- E:\JYZS\ebbinghaus-backend\[eval]
    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)
    at Function._load (node:internal/modules/cjs/loader:1055:27)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)
    at Module.require (node:internal/modules/cjs/loader:1311:12)
    at require (node:internal/modules/helpers:136:16)
    at Object.<anonymous> (E:\JYZS\ebbinghaus-backend\src\middleware\rateLimit.js:3:20)
    at Module._compile (node:internal/modules/cjs/loader:1554:14)
    at Object..js (node:internal/modules/cjs/loader:1706:10)
    at Module.load (node:internal/modules/cjs/loader:1289:32) {"error":{"code":"MODULE_NOT_FOUND","requireStack":["E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js","E:\\JYZS\\ebbinghaus-backend\\[eval]"]},"stack":"node:internal/modules/cjs/loader:1228\n  throw err;\n  ^\n\nError: Cannot find module 'rate-limit-redis'\nRequire stack:\n- E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js\n- E:\\JYZS\\ebbinghaus-backend\\[eval]\n    at Function._resolveFilename (node:internal/modules/cjs/loader:1225:15)\n    at Function._load (node:internal/modules/cjs/loader:1055:27)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at Object.<anonymous> (E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js:3:20)\n    at Module._compile (node:internal/modules/cjs/loader:1554:14)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)","exception":true,"date":"Sun Aug 03 2025 14:20:34 GMT+0800 (中国标准时间)","process":{"pid":22720,"uid":null,"gid":null,"cwd":"E:\\JYZS\\ebbinghaus-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v22.14.0","argv":["C:\\Program Files\\nodejs\\node.exe"],"memoryUsage":{"rss":65994752,"heapTotal":31698944,"heapUsed":13483680,"external":2161224,"arrayBuffers":49643}},"os":{"loadavg":[0,0,0],"uptime":19814.375},"trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Function._resolveFilename","line":1225,"method":"_resolveFilename","native":false},{"column":27,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1055,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":220,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1311,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":136,"method":null,"native":false},{"column":20,"file":"E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\rateLimit.js","function":null,"line":3,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1554,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1706,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1289,"method":"load","native":false}],"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 14:28:36 [ERROR]: MongoDB connection failed {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 12*******:27017","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 12*******:27017\n    at _handleConnectionErrors (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async DatabaseConfig.connect (E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js:57:7)\n    at async initializeServices (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:18:5)\n    at async main (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:178:5)"}
2025-08-03 14:30:47 [ERROR]: MongoDB connection failed {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 12*******:27017","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 12*******:27017\n    at _handleConnectionErrors (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async DatabaseConfig.connect (E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js:57:7)\n    at async initializeServices (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:18:5)\n    at async main (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:178:5)"}
2025-08-03 14:31:16 [ERROR]: MongoDB connection failed {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 12*******:27017","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 12*******:27017\n    at _handleConnectionErrors (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async DatabaseConfig.connect (E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js:57:7)\n    at async initializeServices (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:18:5)\n    at async Object.main (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:178:5)"}
2025-08-03 14:32:24 [ERROR]: uncaughtException: Missing parameter name at 1: https://git.new/pathToRegexpError
TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError
    at name (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:73:19)
    at lexer (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:91:27)
    at lexer.next (<anonymous>)
    at Iter.peek (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:106:38)
    at Iter.tryConsume (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:112:28)
    at Iter.text (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:128:30)
    at consume (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:152:29)
    at parse (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:183:20)
    at E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:294:74
    at Array.map (<anonymous>) {"error":{},"stack":"TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n    at name (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:73:19)\n    at lexer (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:91:27)\n    at lexer.next (<anonymous>)\n    at Iter.peek (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:106:38)\n    at Iter.tryConsume (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:112:28)\n    at Iter.text (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:128:30)\n    at consume (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:152:29)\n    at parse (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:183:20)\n    at E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:294:74\n    at Array.map (<anonymous>)","exception":true,"date":"Sun Aug 03 2025 14:32:24 GMT+0800 (中国标准时间)","process":{"pid":11160,"uid":null,"gid":null,"cwd":"E:\\JYZS\\ebbinghaus-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v22.14.0","argv":["C:\\Program Files\\nodejs\\node.exe","E:\\JYZS\\ebbinghaus-backend\\test-server.js"],"memoryUsage":{"rss":54763520,"heapTotal":18251776,"heapUsed":11866448,"external":2044946,"arrayBuffers":16659}},"os":{"loadavg":[0,0,0],"uptime":20524.468},"trace":[{"column":19,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"name","line":73,"method":null,"native":false},{"column":27,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"lexer","line":91,"method":null,"native":false},{"column":null,"file":null,"function":"lexer.next","line":null,"method":"next","native":false},{"column":38,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"Iter.peek","line":106,"method":"peek","native":false},{"column":28,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"Iter.tryConsume","line":112,"method":"tryConsume","native":false},{"column":30,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"Iter.text","line":128,"method":"text","native":false},{"column":29,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"consume","line":152,"method":null,"native":false},{"column":20,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"parse","line":183,"method":null,"native":false},{"column":74,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":null,"line":294,"method":null,"native":false},{"column":null,"file":null,"function":"Array.map","line":null,"method":"map","native":false}],"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 14:32:55 [ERROR]: uncaughtException: Missing parameter name at 1: https://git.new/pathToRegexpError
E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:73
            throw new TypeError(`Missing parameter name at ${i}: ${DEBUG_URL}`);
            ^

TypeError: Missing parameter name at 1: https://git.new/pathToRegexpError
    at name (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:73:19)
    at lexer (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:91:27)
    at lexer.next (<anonymous>)
    at Iter.peek (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:106:38)
    at Iter.tryConsume (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:112:28)
    at Iter.text (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:128:30)
    at consume (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:152:29)
    at parse (E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:183:20)
    at E:\JYZS\ebbinghaus-backend\node_modules\path-to-regexp\dist\index.js:294:74
    at Array.map (<anonymous>) {"error":{},"stack":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:73\n            throw new TypeError(`Missing parameter name at ${i}: ${DEBUG_URL}`);\n            ^\n\nTypeError: Missing parameter name at 1: https://git.new/pathToRegexpError\n    at name (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:73:19)\n    at lexer (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:91:27)\n    at lexer.next (<anonymous>)\n    at Iter.peek (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:106:38)\n    at Iter.tryConsume (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:112:28)\n    at Iter.text (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:128:30)\n    at consume (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:152:29)\n    at parse (E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:183:20)\n    at E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js:294:74\n    at Array.map (<anonymous>)","exception":true,"date":"Sun Aug 03 2025 14:32:55 GMT+0800 (中国标准时间)","process":{"pid":23908,"uid":null,"gid":null,"cwd":"E:\\JYZS\\ebbinghaus-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v22.14.0","argv":["C:\\Program Files\\nodejs\\node.exe"],"memoryUsage":{"rss":54296576,"heapTotal":18567168,"heapUsed":11150008,"external":2114980,"arrayBuffers":16659}},"os":{"loadavg":[0,0,0],"uptime":20556.031},"trace":[{"column":null,"file":null,"function":null,"line":null,"method":null,"native":false},{"column":null,"file":null,"function":null,"line":null,"method":null,"native":false},{"column":19,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"name","line":73,"method":null,"native":false},{"column":27,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"lexer","line":91,"method":null,"native":false},{"column":null,"file":null,"function":"lexer.next","line":null,"method":"next","native":false},{"column":38,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"Iter.peek","line":106,"method":"peek","native":false},{"column":28,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"Iter.tryConsume","line":112,"method":"tryConsume","native":false},{"column":30,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"Iter.text","line":128,"method":"text","native":false},{"column":29,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"consume","line":152,"method":null,"native":false},{"column":20,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":"parse","line":183,"method":null,"native":false},{"column":74,"file":"E:\\JYZS\\ebbinghaus-backend\\node_modules\\path-to-regexp\\dist\\index.js","function":null,"line":294,"method":null,"native":false},{"column":null,"file":null,"function":"Array.map","line":null,"method":"map","native":false}],"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:30 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:32 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:32 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:32 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:33 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:33 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:34 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:35 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:35 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:36 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:37 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:37 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:38 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:39 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:40 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:41 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:42 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:43 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:45 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:46 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:47 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:48 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:50 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:51 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:53 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:54 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:56 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:57 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:30:59 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:01 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:02 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:04 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:06 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:07 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 16:31:08 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:10 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:12 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:14 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:16 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:18 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:20 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:22 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:24 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:26 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:28 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:30 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:32 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:31:34 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 12*******:6379"}
2025-08-03 16:49:29 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 16:50:09 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 16:53:48 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 16:54:21 [ERROR]: uncaughtException: Cannot set headers after they are sent to the client
Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:699:11)
    at ServerResponse.<anonymous> (E:\JYZS\ebbinghaus-backend\src\middleware\errorHandler.js:237:9)
    at ServerResponse.emit (node:events:530:35)
    at onFinish (node:_http_outgoing:1081:10)
    at callback (node:internal/streams/writable:766:21)
    at afterWrite (node:internal/streams/writable:710:5)
    at afterWriteTick (node:internal/streams/writable:696:10)
    at process.processTicksAndRejections (node:internal/process/task_queues:89:21) {"error":{"code":"ERR_HTTP_HEADERS_SENT"},"stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.<anonymous> (E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js:237:9)\n    at ServerResponse.emit (node:events:530:35)\n    at onFinish (node:_http_outgoing:1081:10)\n    at callback (node:internal/streams/writable:766:21)\n    at afterWrite (node:internal/streams/writable:710:5)\n    at afterWriteTick (node:internal/streams/writable:696:10)\n    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)","exception":true,"date":"Sun Aug 03 2025 16:54:21 GMT+0800 (中国标准时间)","process":{"pid":27184,"uid":null,"gid":null,"cwd":"E:\\JYZS\\ebbinghaus-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v22.14.0","argv":["C:\\Program Files\\nodejs\\node.exe","E:\\JYZS\\ebbinghaus-backend\\src\\server.js"],"memoryUsage":{"rss":76566528,"heapTotal":31268864,"heapUsed":28525216,"external":20729960,"arrayBuffers":18330161}},"os":{"loadavg":[0,0,0],"uptime":29041.593},"trace":[{"column":11,"file":"node:_http_outgoing","function":"ServerResponse.setHeader","line":699,"method":"setHeader","native":false},{"column":9,"file":"E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js","function":null,"line":237,"method":null,"native":false},{"column":35,"file":"node:events","function":"ServerResponse.emit","line":530,"method":"emit","native":false},{"column":10,"file":"node:_http_outgoing","function":"onFinish","line":1081,"method":null,"native":false},{"column":21,"file":"node:internal/streams/writable","function":"callback","line":766,"method":null,"native":false},{"column":5,"file":"node:internal/streams/writable","function":"afterWrite","line":710,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"afterWriteTick","line":696,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":89,"method":"processTicksAndRejections","native":false}],"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 17:05:09 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:05:09 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:05:09 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"content":"No title"},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:05:09 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:05:09 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:05:09 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:05:09 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":{"title":"Updated Title"},"error":"Database error"}
2025-08-03 17:05:09 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":{},"error":"Database error"}
2025-08-03 17:05:09 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":"user123","error":"Database error"}
2025-08-03 17:05:09 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":"user123","error":"Database error"}
2025-08-03 17:05:09 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"test","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 17:05:09 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 17:05:09 [ERROR]: Failed to get task statistics {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Cannot read properties of undefined (reading 'total')"}
2025-08-03 17:08:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:08:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:08:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"content":"No title"},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:08:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:08:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:08:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:08:38 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":{"title":"Updated Title"},"error":"Database error"}
2025-08-03 17:08:38 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":{},"error":"Database error"}
2025-08-03 17:08:38 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":"user123","error":"Database error"}
2025-08-03 17:08:38 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":"user123","error":"Database error"}
2025-08-03 17:08:38 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"test","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 17:08:38 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 17:08:38 [ERROR]: Failed to get task statistics {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Cannot read properties of undefined (reading 'total')"}
2025-08-03 18:31:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 18:31:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 18:31:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"content":"No title"},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 18:31:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 18:31:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 18:31:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 18:31:38 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":{"title":"Updated Title"},"error":"Database error"}
2025-08-03 18:31:38 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":{},"error":"Database error"}
2025-08-03 18:31:38 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":"user123","error":"Database error"}
2025-08-03 18:31:38 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":"user123","error":"Database error"}
2025-08-03 18:31:38 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"test","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 18:31:38 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 18:31:38 [ERROR]: Failed to get task statistics {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Cannot read properties of undefined (reading 'total')"}
2025-08-03 19:13:08 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 19:17:25 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 19:18:41 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 19:18:54 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 19:22:45 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 19:23:22 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 19:23:39 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 19:24:09 [ERROR]: uncaughtException: Cannot set headers after they are sent to the client
Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:699:11)
    at ServerResponse.<anonymous> (E:\JYZS\ebbinghaus-backend\src\middleware\errorHandler.js:237:9)
    at ServerResponse.emit (node:events:530:35)
    at onFinish (node:_http_outgoing:1081:10)
    at callback (node:internal/streams/writable:766:21)
    at afterWrite (node:internal/streams/writable:710:5)
    at afterWriteTick (node:internal/streams/writable:696:10)
    at process.processTicksAndRejections (node:internal/process/task_queues:89:21) {"error":{"code":"ERR_HTTP_HEADERS_SENT"},"stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.<anonymous> (E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js:237:9)\n    at ServerResponse.emit (node:events:530:35)\n    at onFinish (node:_http_outgoing:1081:10)\n    at callback (node:internal/streams/writable:766:21)\n    at afterWrite (node:internal/streams/writable:710:5)\n    at afterWriteTick (node:internal/streams/writable:696:10)\n    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)","exception":true,"date":"Sun Aug 03 2025 19:24:09 GMT+0800 (中国标准时间)","process":{"pid":26012,"uid":null,"gid":null,"cwd":"E:\\JYZS\\ebbinghaus-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v22.14.0","argv":["C:\\Program Files\\nodejs\\node.exe","E:\\JYZS\\ebbinghaus-backend\\src\\server.js"],"memoryUsage":{"rss":79671296,"heapTotal":34152448,"heapUsed":31504832,"external":20741862,"arrayBuffers":18329535}},"os":{"loadavg":[0,0,0],"uptime":38029.171},"trace":[{"column":11,"file":"node:_http_outgoing","function":"ServerResponse.setHeader","line":699,"method":"setHeader","native":false},{"column":9,"file":"E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js","function":null,"line":237,"method":null,"native":false},{"column":35,"file":"node:events","function":"ServerResponse.emit","line":530,"method":"emit","native":false},{"column":10,"file":"node:_http_outgoing","function":"onFinish","line":1081,"method":null,"native":false},{"column":21,"file":"node:internal/streams/writable","function":"callback","line":766,"method":null,"native":false},{"column":5,"file":"node:internal/streams/writable","function":"afterWrite","line":710,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"afterWriteTick","line":696,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":89,"method":"processTicksAndRejections","native":false}],"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:32:16 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 21:32:32 [ERROR]: uncaughtException: Cannot set headers after they are sent to the client
Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client
    at ServerResponse.setHeader (node:_http_outgoing:699:11)
    at ServerResponse.<anonymous> (E:\JYZS\ebbinghaus-backend\src\middleware\errorHandler.js:237:9)
    at ServerResponse.emit (node:events:530:35)
    at onFinish (node:_http_outgoing:1081:10)
    at callback (node:internal/streams/writable:766:21)
    at afterWrite (node:internal/streams/writable:710:5)
    at afterWriteTick (node:internal/streams/writable:696:10)
    at process.processTicksAndRejections (node:internal/process/task_queues:89:21) {"error":{"code":"ERR_HTTP_HEADERS_SENT"},"stack":"Error [ERR_HTTP_HEADERS_SENT]: Cannot set headers after they are sent to the client\n    at ServerResponse.setHeader (node:_http_outgoing:699:11)\n    at ServerResponse.<anonymous> (E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js:237:9)\n    at ServerResponse.emit (node:events:530:35)\n    at onFinish (node:_http_outgoing:1081:10)\n    at callback (node:internal/streams/writable:766:21)\n    at afterWrite (node:internal/streams/writable:710:5)\n    at afterWriteTick (node:internal/streams/writable:696:10)\n    at process.processTicksAndRejections (node:internal/process/task_queues:89:21)","exception":true,"date":"Sun Aug 03 2025 21:32:32 GMT+0800 (中国标准时间)","process":{"pid":22944,"uid":null,"gid":null,"cwd":"E:\\JYZS\\ebbinghaus-backend","execPath":"C:\\Program Files\\nodejs\\node.exe","version":"v22.14.0","argv":["C:\\Program Files\\nodejs\\node.exe","E:\\JYZS\\ebbinghaus-backend\\src\\server.js"],"memoryUsage":{"rss":79491072,"heapTotal":34152448,"heapUsed":31348688,"external":20758527,"arrayBuffers":18320893}},"os":{"loadavg":[0,0,0],"uptime":45733.062},"trace":[{"column":11,"file":"node:_http_outgoing","function":"ServerResponse.setHeader","line":699,"method":"setHeader","native":false},{"column":9,"file":"E:\\JYZS\\ebbinghaus-backend\\src\\middleware\\errorHandler.js","function":null,"line":237,"method":null,"native":false},{"column":35,"file":"node:events","function":"ServerResponse.emit","line":530,"method":"emit","native":false},{"column":10,"file":"node:_http_outgoing","function":"onFinish","line":1081,"method":null,"native":false},{"column":21,"file":"node:internal/streams/writable","function":"callback","line":766,"method":null,"native":false},{"column":5,"file":"node:internal/streams/writable","function":"afterWrite","line":710,"method":null,"native":false},{"column":10,"file":"node:internal/streams/writable","function":"afterWriteTick","line":696,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"process.processTicksAndRejections","line":89,"method":"processTicksAndRejections","native":false}],"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:55:16 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 21:57:41 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 21:59:44 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","error":"Cast to ObjectId failed for value \"13289601-6713-4725-a516-f9d1f7886a4d\" (type string) at path \"userId\" for model \"Task\""}
2025-08-03 21:59:44 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","error":"Cast to ObjectId failed for value \"13289601-6713-4725-a516-f9d1f7886a4d\" (type string) at path \"userId\" for model \"Task\""}
2025-08-03 22:00:15 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:00:51 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:01:06 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:01:26 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:01:47 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:09:43 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:14:09 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:21:57 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:22:34 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:23:40 [ERROR]: Failed to start server {"service":"ebbinghaus-backend","environment":"development","error":"listen EADDRINUSE: address already in use :::3002","port":"3002"}
