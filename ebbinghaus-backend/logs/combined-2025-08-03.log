2025-08-03 14:28:30 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 14:28:30 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 14:28:30 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 14:28:36 [ERROR]: MongoDB connection failed {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async DatabaseConfig.connect (E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js:57:7)\n    at async initializeServices (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:18:5)\n    at async main (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:178:5)"}
2025-08-03 14:30:42 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 14:30:42 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 14:30:42 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 14:30:47 [ERROR]: MongoDB connection failed {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async DatabaseConfig.connect (E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js:57:7)\n    at async initializeServices (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:18:5)\n    at async main (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:178:5)"}
2025-08-03 14:31:11 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 14:31:11 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 14:31:11 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 14:31:16 [ERROR]: MongoDB connection failed {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017","stack":"MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n    at _handleConnectionErrors (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n    at NativeConnection.openUri (E:\\JYZS\\ebbinghaus-backend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n    at async DatabaseConfig.connect (E:\\JYZS\\ebbinghaus-backend\\src\\config\\database.js:57:7)\n    at async initializeServices (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:18:5)\n    at async Object.main (E:\\JYZS\\ebbinghaus-backend\\src\\server.js:178:5)"}
2025-08-03 14:33:40 [INFO]: Test server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3000","mode":"test"}
2025-08-03 14:34:41 [INFO]: Test server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3001","mode":"test"}
2025-08-03 14:35:26 [INFO]: Test server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3001","mode":"test"}
2025-08-03 14:52:15 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 14:52:15 [INFO]: Using Memory Database for development... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 14:52:15 [INFO]: Starting MongoDB Memory Server... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 14:52:53 [INFO]: Starting MongoDB Memory Server... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:29:56 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 16:29:56 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 16:30:14 [WARN]: Mongoose disconnected from MongoDB {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:30 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:30 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:30 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 16:30:30 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 16:30:30 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:30 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 16:30:30 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 16:30:30 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:30 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:31 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3001","nodeVersion":"v22.14.0","pid":21324}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:31 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:31 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":50}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:31 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:31 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":100}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:31 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:31 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":150}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:31 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:31 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":200}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:31 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:31 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":250}
2025-08-03 16:30:31 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:31 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:31 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":300}
2025-08-03 16:30:32 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:32 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:32 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":350}
2025-08-03 16:30:32 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:32 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:32 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":400}
2025-08-03 16:30:32 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:32 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:32 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":450}
2025-08-03 16:30:33 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:33 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:33 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":500}
2025-08-03 16:30:33 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:33 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:33 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":550}
2025-08-03 16:30:34 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:34 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:34 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":600}
2025-08-03 16:30:35 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:35 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:35 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":650}
2025-08-03 16:30:35 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:35 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:35 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":700}
2025-08-03 16:30:36 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:36 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:36 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":750}
2025-08-03 16:30:37 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:37 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:37 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":800}
2025-08-03 16:30:37 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:37 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:37 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":850}
2025-08-03 16:30:38 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:38 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:38 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":900}
2025-08-03 16:30:39 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:39 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:39 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":950}
2025-08-03 16:30:40 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:40 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:40 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1000}
2025-08-03 16:30:41 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:41 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:41 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1050}
2025-08-03 16:30:42 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:42 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:42 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1100}
2025-08-03 16:30:43 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:43 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:43 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1150}
2025-08-03 16:30:45 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:45 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:45 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1200}
2025-08-03 16:30:46 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:46 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:46 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1250}
2025-08-03 16:30:47 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:47 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:47 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1300}
2025-08-03 16:30:48 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:48 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:48 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1350}
2025-08-03 16:30:50 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:50 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:50 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1400}
2025-08-03 16:30:51 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:51 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:51 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1450}
2025-08-03 16:30:53 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:53 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:53 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1500}
2025-08-03 16:30:54 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:54 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:54 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1550}
2025-08-03 16:30:56 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:56 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:56 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1600}
2025-08-03 16:30:57 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:57 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:57 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1650}
2025-08-03 16:30:59 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:30:59 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:30:59 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1700}
2025-08-03 16:31:01 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:01 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:01 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1750}
2025-08-03 16:31:02 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:02 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:02 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1800}
2025-08-03 16:31:04 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:04 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:04 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1850}
2025-08-03 16:31:06 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:06 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:06 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1900}
2025-08-03 16:31:07 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:07 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:07 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 16:31:07 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 16:31:08 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:08 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:08 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":1950}
2025-08-03 16:31:10 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:10 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:10 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:12 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:12 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:12 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:14 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:14 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:14 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:16 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:16 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:16 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:18 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:18 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:18 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:20 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:20 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:20 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:22 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:22 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:22 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:24 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:24 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:24 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:26 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:26 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:26 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:28 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:28 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:28 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:30 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:30 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:30 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:32 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:32 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:32 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:31:34 [ERROR]: Redis client error {"service":"ebbinghaus-backend","environment":"development","error":"connect ECONNREFUSED 127.0.0.1:6379"}
2025-08-03 16:31:34 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:31:34 [INFO]: Redis client reconnecting {"service":"ebbinghaus-backend","environment":"development","delay":2000}
2025-08-03 16:32:08 [INFO]: Test server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","mode":"test"}
2025-08-03 16:33:35 [INFO]: Test server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","mode":"test"}
2025-08-03 16:49:29 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 16:49:29 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 16:49:29 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:49:29 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:49:29 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:49:47 [INFO]: Redis disconnected successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:49:47 [INFO]: Redis connection closed through app termination {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:49:47 [WARN]: Redis client connection closed {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:50:09 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:50:09 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:50:09 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 16:50:09 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 16:50:09 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:50:09 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 16:50:09 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 16:50:09 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:50:09 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:50:09 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":26452}
2025-08-03 16:50:09 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:50:09 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:53:48 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:53:48 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:53:48 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 16:53:48 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 16:53:48 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:53:48 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 16:53:48 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 16:53:48 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:53:48 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:53:48 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":27184}
2025-08-03 16:53:48 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:53:48 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 16:54:21 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/health","method":"GET","statusCode":200,"responseTime":"6ms","requestId":"bc56d67c-b64b-48c9-91ae-a3448e4c1b55"}
2025-08-03 17:05:09 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:05:09 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:05:09 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"content":"No title"},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:05:09 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:05:09 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:05:09 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:05:09 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":{"title":"Updated Title"},"error":"Database error"}
2025-08-03 17:05:09 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":{},"error":"Database error"}
2025-08-03 17:05:09 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":"user123","error":"Database error"}
2025-08-03 17:05:09 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":"user123","error":"Database error"}
2025-08-03 17:05:09 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"test","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 17:05:09 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 17:05:09 [ERROR]: Failed to get task statistics {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Cannot read properties of undefined (reading 'total')"}
2025-08-03 17:05:10 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"newpassword123","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 17:05:10 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"20ms","requestId":"445aa6dc-ce9a-4463-bea7-56d87e032ede"}
2025-08-03 17:05:10 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"invalid-email","msg":"Please provide a valid email address","path":"email","location":"body"},{"type":"field","value":"password123","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 17:05:10 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"3ms","requestId":"d2570990-1174-4c1b-bb7b-abeab9c2fe25"}
2025-08-03 17:05:10 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"123","msg":"Password must be at least 6 characters long","path":"password","location":"body"},{"type":"field","value":"123","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 17:05:10 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"2ms","requestId":"1aa88b8f-adba-445a-b750-0591c4a625f4"}
2025-08-03 17:05:10 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"","msg":"Username must be between 3 and 50 characters","path":"username","location":"body"},{"type":"field","value":"","msg":"Username can only contain letters, numbers, and underscores","path":"username","location":"body"},{"type":"field","msg":"Password must be at least 6 characters long","path":"password","location":"body"},{"type":"field","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 17:05:10 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"3ms","requestId":"808f6bf8-c00d-4912-8e99-38913bd62c45"}
2025-08-03 17:05:10 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/logout"}
2025-08-03 17:05:10 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/logout","method":"POST","statusCode":401,"responseTime":"3ms","requestId":"9a5b4deb-c91c-4fdf-9cfe-48ee542d8d2d"}
2025-08-03 17:05:10 [INFO]: [AUTH] Token verification failed {"service":"ebbinghaus-backend","environment":"test","error":"jwt malformed","tokenPreview":"invalid-token..."}
2025-08-03 17:05:10 [WARN]: [SECURITY] Token authentication failed {"service":"ebbinghaus-backend","environment":"test","error":"TOKEN_INVALID","ip":"::ffff:127.0.0.1","path":"/logout"}
2025-08-03 17:05:10 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/logout","method":"POST","statusCode":401,"responseTime":"2ms","requestId":"39a0732b-23bf-4ba7-851a-03b3bc3953ef"}
2025-08-03 17:05:10 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/me"}
2025-08-03 17:05:10 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/me","method":"GET","statusCode":401,"responseTime":"1ms","requestId":"e619f190-d8a7-4228-8a66-bf325fe7532e"}
2025-08-03 17:05:10 [INFO]: [AUTH] Token verification failed {"service":"ebbinghaus-backend","environment":"test","error":"jwt malformed","tokenPreview":"invalid-token..."}
2025-08-03 17:05:10 [WARN]: [SECURITY] Token authentication failed {"service":"ebbinghaus-backend","environment":"test","error":"TOKEN_INVALID","ip":"::ffff:127.0.0.1","path":"/me"}
2025-08-03 17:05:10 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/me","method":"GET","statusCode":401,"responseTime":"2ms","requestId":"e0849b80-cca1-41e3-88ed-e6c5d606658d"}
2025-08-03 17:05:10 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/profile"}
2025-08-03 17:05:10 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/profile","method":"PUT","statusCode":401,"responseTime":"2ms","requestId":"4e9fc810-86e2-43e1-9d08-95f06ed7ce83"}
2025-08-03 17:05:10 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/change-password"}
2025-08-03 17:05:10 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/change-password","method":"POST","statusCode":401,"responseTime":"1ms","requestId":"96f156a3-e10c-4ebb-b7ac-9598904682de"}
2025-08-03 17:08:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:08:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:08:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"content":"No title"},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 17:08:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:08:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:08:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 17:08:38 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":{"title":"Updated Title"},"error":"Database error"}
2025-08-03 17:08:38 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":{},"error":"Database error"}
2025-08-03 17:08:38 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":"user123","error":"Database error"}
2025-08-03 17:08:38 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":"user123","error":"Database error"}
2025-08-03 17:08:38 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"test","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 17:08:38 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 17:08:38 [ERROR]: Failed to get task statistics {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Cannot read properties of undefined (reading 'total')"}
2025-08-03 17:10:27 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"newpassword123","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"22ms","requestId":"2ee5a76b-16a1-4c93-a502-cea252f2fe1a"}
2025-08-03 17:10:27 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"invalid-email","msg":"Please provide a valid email address","path":"email","location":"body"},{"type":"field","value":"password123","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"5ms","requestId":"9ce89fd1-6ded-4f95-a804-ffc99a631d21"}
2025-08-03 17:10:27 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"123","msg":"Password must be at least 6 characters long","path":"password","location":"body"},{"type":"field","value":"123","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"3ms","requestId":"a3e2e4bf-9174-4628-80d7-4522367a701e"}
2025-08-03 17:10:27 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"","msg":"Username must be between 3 and 50 characters","path":"username","location":"body"},{"type":"field","value":"","msg":"Username can only contain letters, numbers, and underscores","path":"username","location":"body"},{"type":"field","msg":"Password must be at least 6 characters long","path":"password","location":"body"},{"type":"field","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"4ms","requestId":"6b74cbca-3544-4b13-9391-3912cbd43dea"}
2025-08-03 17:10:27 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/logout"}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/logout","method":"POST","statusCode":401,"responseTime":"4ms","requestId":"941142ea-d17d-4d3f-afb3-e224a81a90e0"}
2025-08-03 17:10:27 [INFO]: [AUTH] Token verification failed {"service":"ebbinghaus-backend","environment":"test","error":"jwt malformed","tokenPreview":"invalid-token..."}
2025-08-03 17:10:27 [WARN]: [SECURITY] Token authentication failed {"service":"ebbinghaus-backend","environment":"test","error":"TOKEN_INVALID","ip":"::ffff:127.0.0.1","path":"/logout"}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/logout","method":"POST","statusCode":401,"responseTime":"3ms","requestId":"a6b3e891-1179-44f4-8c07-f26d7672269a"}
2025-08-03 17:10:27 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/me"}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/me","method":"GET","statusCode":401,"responseTime":"2ms","requestId":"caa8911f-44bb-4541-90a7-6f9b4763bbde"}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/health","method":"GET","statusCode":503,"responseTime":"8ms","requestId":"3d25fca1-353b-4186-b6da-c80a2fc6aa90"}
2025-08-03 17:10:27 [INFO]: [AUTH] Token verification failed {"service":"ebbinghaus-backend","environment":"test","error":"jwt malformed","tokenPreview":"invalid-token..."}
2025-08-03 17:10:27 [WARN]: [SECURITY] Token authentication failed {"service":"ebbinghaus-backend","environment":"test","error":"TOKEN_INVALID","ip":"::ffff:127.0.0.1","path":"/me"}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/me","method":"GET","statusCode":401,"responseTime":"1ms","requestId":"d78c0782-319b-4848-b088-ea616fe15398"}
2025-08-03 17:10:27 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/profile"}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/profile","method":"PUT","statusCode":401,"responseTime":"2ms","requestId":"4be3762b-6fa1-4b71-a6c6-4641d6a8d500"}
2025-08-03 17:10:27 [INFO]: [API] Route not found {"service":"ebbinghaus-backend","environment":"test","path":"/api/health","method":"GET","ip":"::ffff:127.0.0.1"}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/api/health","method":"GET","statusCode":404,"responseTime":"2ms","requestId":"fe20a1ec-ab9d-4842-b883-599779d3d1b5"}
2025-08-03 17:10:27 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/change-password"}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/change-password","method":"POST","statusCode":401,"responseTime":"2ms","requestId":"6ca5a49a-b603-4fad-88b1-d365596757fc"}
2025-08-03 17:10:27 [INFO]: [API] Route not found {"service":"ebbinghaus-backend","environment":"test","path":"/non-existent-route","method":"GET","ip":"::ffff:127.0.0.1"}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/non-existent-route","method":"GET","statusCode":404,"responseTime":"1ms","requestId":"96be6ca0-3b39-40fd-be31-c03cbeec3427"}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/health","method":"GET","statusCode":503,"responseTime":"1ms","requestId":"605e6147-72bb-4fc3-8edb-c4db12487768"}
2025-08-03 17:10:27 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/health","method":"GET","statusCode":503,"responseTime":"1ms","requestId":"79e448fb-de9d-4137-82c4-56a9bc4ccd0c"}
2025-08-03 18:31:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 18:31:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"title":"English Vocabulary","content":"Learn new English words","type":"vocabulary","difficulty":"medium","tags":["english","vocabulary"],"metadata":{"source":"textbook","category":"language"}},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 18:31:38 [ERROR]: Failed to create task {"service":"ebbinghaus-backend","environment":"test","userId":{"content":"No title"},"error":"Cannot read properties of undefined (reading 'estimatedTime')"}
2025-08-03 18:31:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 18:31:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 18:31:38 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Task.find(...).sort(...).skip(...).limit(...).lean is not a function"}
2025-08-03 18:31:38 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":{"title":"Updated Title"},"error":"Database error"}
2025-08-03 18:31:38 [ERROR]: Failed to update task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":{},"error":"Database error"}
2025-08-03 18:31:38 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"task123","userId":"user123","error":"Database error"}
2025-08-03 18:31:38 [ERROR]: Failed to delete task {"service":"ebbinghaus-backend","environment":"test","taskId":"nonexistent","userId":"user123","error":"Database error"}
2025-08-03 18:31:38 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"test","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 18:31:38 [ERROR]: Failed to search tasks {"service":"ebbinghaus-backend","environment":"test","userId":"user123","searchQuery":"","error":"Cannot read properties of undefined (reading 'length')"}
2025-08-03 18:31:38 [ERROR]: Failed to get task statistics {"service":"ebbinghaus-backend","environment":"test","userId":"user123","error":"Cannot read properties of undefined (reading 'total')"}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/health","method":"GET","statusCode":503,"responseTime":"7ms","requestId":"f2b5c750-9f69-4419-9475-77707bceaa27"}
2025-08-03 18:31:39 [INFO]: [API] Route not found {"service":"ebbinghaus-backend","environment":"test","path":"/api/health","method":"GET","ip":"::ffff:127.0.0.1"}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/api/health","method":"GET","statusCode":404,"responseTime":"2ms","requestId":"d4772776-350b-4b92-b704-06e83ee2303f"}
2025-08-03 18:31:39 [INFO]: [API] Route not found {"service":"ebbinghaus-backend","environment":"test","path":"/non-existent-route","method":"GET","ip":"::ffff:127.0.0.1"}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/non-existent-route","method":"GET","statusCode":404,"responseTime":"2ms","requestId":"cd7b6d60-9cf6-4ccd-84f7-b5a72b910781"}
2025-08-03 18:31:39 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"newpassword123","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/health","method":"GET","statusCode":503,"responseTime":"2ms","requestId":"294e62d8-1315-47e5-9652-7b49f63dde01"}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/health","method":"GET","statusCode":503,"responseTime":"2ms","requestId":"5d9b4ef8-35cf-4fb2-bf1a-6beb3f773a38"}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"23ms","requestId":"20f08d8a-72e3-415c-99c0-e750a4131957"}
2025-08-03 18:31:39 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"invalid-email","msg":"Please provide a valid email address","path":"email","location":"body"},{"type":"field","value":"password123","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"10ms","requestId":"66f172c4-89f9-4234-8711-2990b2a3c9ae"}
2025-08-03 18:31:39 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"123","msg":"Password must be at least 6 characters long","path":"password","location":"body"},{"type":"field","value":"123","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"5ms","requestId":"273c8f2c-6c82-45ef-9168-3ed769236cf0"}
2025-08-03 18:31:39 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","errors":[{"type":"field","value":"","msg":"Username must be between 3 and 50 characters","path":"username","location":"body"},{"type":"field","value":"","msg":"Username can only contain letters, numbers, and underscores","path":"username","location":"body"},{"type":"field","msg":"Password must be at least 6 characters long","path":"password","location":"body"},{"type":"field","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/register","method":"POST","statusCode":400,"responseTime":"2ms","requestId":"237b5645-c39f-4b75-b060-ce447204cbf2"}
2025-08-03 18:31:39 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/logout"}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/logout","method":"POST","statusCode":401,"responseTime":"5ms","requestId":"dc33aee8-b7b6-47ab-914d-d44fd2b1ec3c"}
2025-08-03 18:31:39 [INFO]: [AUTH] Token verification failed {"service":"ebbinghaus-backend","environment":"test","error":"jwt malformed","tokenPreview":"invalid-token..."}
2025-08-03 18:31:39 [WARN]: [SECURITY] Token authentication failed {"service":"ebbinghaus-backend","environment":"test","error":"TOKEN_INVALID","ip":"::ffff:127.0.0.1","path":"/logout"}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/logout","method":"POST","statusCode":401,"responseTime":"3ms","requestId":"db7fd4d2-c884-46aa-9105-1f12050528b7"}
2025-08-03 18:31:39 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/me"}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/me","method":"GET","statusCode":401,"responseTime":"2ms","requestId":"42954420-491f-4953-aa0e-f41bc29cefd8"}
2025-08-03 18:31:39 [INFO]: [AUTH] Token verification failed {"service":"ebbinghaus-backend","environment":"test","error":"jwt malformed","tokenPreview":"invalid-token..."}
2025-08-03 18:31:39 [WARN]: [SECURITY] Token authentication failed {"service":"ebbinghaus-backend","environment":"test","error":"TOKEN_INVALID","ip":"::ffff:127.0.0.1","path":"/me"}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/me","method":"GET","statusCode":401,"responseTime":"2ms","requestId":"f9ebc792-e5d7-4314-8ddc-6cbff6de438e"}
2025-08-03 18:31:39 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/profile"}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/profile","method":"PUT","statusCode":401,"responseTime":"3ms","requestId":"4abe88c9-9b92-4e30-b405-df46736a9db2"}
2025-08-03 18:31:39 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"test","ip":"::ffff:127.0.0.1","path":"/change-password"}
2025-08-03 18:31:39 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"test","path":"/change-password","method":"POST","statusCode":401,"responseTime":"2ms","requestId":"e066bd95-98af-471b-9a04-45e2efc59638"}
2025-08-03 18:39:11 [INFO]: Test server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"8080","mode":"test"}
2025-08-03 18:45:55 [INFO]: Test server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"8080","mode":"test"}
2025-08-03 18:51:30 [INFO]: Test server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"8080","mode":"test"}
2025-08-03 19:07:05 [INFO]: Test server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","mode":"test"}
2025-08-03 19:13:08 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:13:08 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:13:08 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 19:13:08 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 19:17:25 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:17:25 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:17:25 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 19:17:25 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 19:18:41 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:18:41 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:18:41 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 19:18:41 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 19:18:54 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:18:54 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:18:54 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 19:18:54 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 19:22:44 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:22:44 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:22:44 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 19:22:44 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 19:22:44 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:22:44 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 19:22:45 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 19:22:45 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:22:45 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:22:45 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":24132}
2025-08-03 19:22:45 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:22:45 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:22 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:22 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:22 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 19:23:22 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 19:23:22 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:22 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 19:23:22 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 19:23:22 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:22 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:22 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":27540}
2025-08-03 19:23:22 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:22 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:39 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:39 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:39 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 19:23:39 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 19:23:39 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:39 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 19:23:39 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 19:23:39 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:39 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:39 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":26012}
2025-08-03 19:23:39 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:23:39 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 19:24:09 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/health","method":"GET","statusCode":200,"responseTime":"7ms","requestId":"18b5f445-70a6-4a65-bed6-ef7cebc7bf56"}
2025-08-03 21:32:16 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:32:16 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:32:16 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 21:32:16 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 21:32:16 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:32:16 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 21:32:16 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 21:32:16 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:32:16 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:32:16 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":22944}
2025-08-03 21:32:16 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:32:16 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:32:32 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/health","method":"GET","statusCode":200,"responseTime":"4ms","requestId":"2077503c-6f62-42d0-9ff7-8da83d1b72f2"}
2025-08-03 21:55:16 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:55:16 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:55:16 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 21:55:16 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 21:55:16 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:55:16 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 21:55:16 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 21:55:16 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:55:16 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:55:16 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":1956}
2025-08-03 21:55:16 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:55:16 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:56:15 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/health","method":"GET","statusCode":200,"responseTime":"2ms","requestId":"c3fef47f-d8b4-46b1-9610-8854313d91d4"}
2025-08-03 21:56:37 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":200,"responseTime":"1ms","requestId":"5e0ec315-ff28-4efa-bdbc-2758fd7e356e"}
2025-08-03 21:56:49 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":301,"responseTime":"2ms","requestId":"0b92659e-58a6-42fb-bfc7-4d6c26d3c0d7"}
2025-08-03 21:56:49 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":200,"responseTime":"1ms","requestId":"c766105c-4b81-460d-9765-a6d10ff1e750"}
2025-08-03 21:57:41 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:57:41 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:57:41 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 21:57:41 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 21:57:41 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:57:41 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 21:57:41 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 21:57:41 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:57:41 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:57:41 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":23324}
2025-08-03 21:57:41 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:57:41 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 21:58:06 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"development","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.5737","path":"/"}
2025-08-03 21:58:06 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":401,"responseTime":"3ms","requestId":"bf17303a-b327-4084-a388-e14ece54dda4"}
2025-08-03 21:58:21 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"development","path":"/register","method":"POST","errors":[{"type":"field","value":"testpassword123","msg":"Password must contain at least one uppercase letter, one lowercase letter, and one number","path":"password","location":"body"}]}
2025-08-03 21:58:21 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/register","method":"POST","statusCode":400,"responseTime":"8ms","requestId":"ee9cc610-3a41-4682-bedf-f252ea5fcc02"}
2025-08-03 21:58:34 [INFO]: [AUTH] User registration attempt {"service":"ebbinghaus-backend","environment":"development","username":"testuser","email":"<EMAIL>"}
2025-08-03 21:58:34 [INFO]: [AUTH] Access token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser"}
2025-08-03 21:58:34 [INFO]: [AUTH] Refresh token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"2894ca03-64b6-48f3-893f-480654d8e444"}
2025-08-03 21:58:34 [INFO]: [AUTH] User registered successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser","email":"<EMAIL>"}
2025-08-03 21:58:34 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/register","method":"POST","statusCode":201,"responseTime":"328ms","requestId":"074a9a94-64bf-4ab4-ad5f-942f8a432abf"}
2025-08-03 21:58:34 [INFO]: [AUTH] Refresh token stored {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"2894ca03-64b6-48f3-893f-480654d8e444","ttl":2592000}
2025-08-03 21:58:50 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"development","path":"/login","method":"POST","errors":[{"type":"field","value":"","msg":"Username or email is required","path":"identifier","location":"body"}]}
2025-08-03 21:58:50 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/login","method":"POST","statusCode":400,"responseTime":"2ms","requestId":"2ec019af-247a-44a0-88bf-9fc848bac3a8"}
2025-08-03 21:59:20 [INFO]: [AUTH] User login attempt {"service":"ebbinghaus-backend","environment":"development","identifier":"<EMAIL>"}
2025-08-03 21:59:20 [INFO]: [AUTH] Access token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser"}
2025-08-03 21:59:20 [INFO]: [AUTH] Refresh token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"174263b7-0fab-4372-8ab7-9c66b4520670"}
2025-08-03 21:59:20 [INFO]: [AUTH] User logged in successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser","email":"<EMAIL>"}
2025-08-03 21:59:20 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/login","method":"POST","statusCode":200,"responseTime":"260ms","requestId":"2932fc55-c724-459f-b2a3-1b694215bc77"}
2025-08-03 21:59:20 [INFO]: [AUTH] Refresh token stored {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"174263b7-0fab-4372-8ab7-9c66b4520670","ttl":2592000}
2025-08-03 21:59:31 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"development","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.5737","path":"/"}
2025-08-03 21:59:31 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":401,"responseTime":"1ms","requestId":"de785ece-cce3-40c7-a8ba-fc9e58a5080b"}
2025-08-03 21:59:44 [INFO]: [AUTH] User login attempt {"service":"ebbinghaus-backend","environment":"development","identifier":"<EMAIL>"}
2025-08-03 21:59:44 [INFO]: [AUTH] Access token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser"}
2025-08-03 21:59:44 [INFO]: [AUTH] Refresh token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"52df2d1b-5b65-459f-ba05-8215462eb331"}
2025-08-03 21:59:44 [INFO]: [AUTH] User logged in successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser","email":"<EMAIL>"}
2025-08-03 21:59:44 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/login","method":"POST","statusCode":200,"responseTime":"266ms","requestId":"c827eeb4-c83f-42ed-a482-7e69c610daa2"}
2025-08-03 21:59:44 [INFO]: [AUTH] Refresh token stored {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"52df2d1b-5b65-459f-ba05-8215462eb331","ttl":2592000}
2025-08-03 21:59:44 [INFO]: [AUTH] Token verified successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","type":"access"}
2025-08-03 21:59:44 [INFO]: [AUTH] Token authenticated successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","path":"/","method":"GET"}
2025-08-03 21:59:44 [INFO]: [API] Getting user tasks {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","page":1,"limit":20,"filters":{}}
2025-08-03 21:59:44 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","error":"Cast to ObjectId failed for value \"13289601-6713-4725-a516-f9d1f7886a4d\" (type string) at path \"userId\" for model \"Task\""}
2025-08-03 21:59:44 [ERROR]: Failed to get user tasks {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","error":"Cast to ObjectId failed for value \"13289601-6713-4725-a516-f9d1f7886a4d\" (type string) at path \"userId\" for model \"Task\""}
2025-08-03 21:59:44 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":500,"responseTime":"9ms","userId":"13289601-6713-4725-a516-f9d1f7886a4d","requestId":"984305d8-c1c4-4278-9a49-3ddc2ee01771"}
2025-08-03 22:00:15 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:15 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:15 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:00:15 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:00:15 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:15 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 22:00:15 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:00:15 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:15 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:15 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":27328}
2025-08-03 22:00:15 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:15 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:51 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:51 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:51 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:00:51 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:00:51 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:51 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 22:00:51 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:00:51 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:51 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:51 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":17888}
2025-08-03 22:00:51 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:00:51 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:06 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:06 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:06 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:01:06 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:01:06 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:06 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 22:01:06 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:01:06 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:06 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:06 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":21292}
2025-08-03 22:01:06 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:06 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:26 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:26 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:26 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:01:26 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:01:26 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:26 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 22:01:26 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:01:26 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:26 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:26 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":24624}
2025-08-03 22:01:26 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:26 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:47 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:47 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:47 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:01:47 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:01:47 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:47 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 22:01:47 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:01:47 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:47 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:47 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":2320}
2025-08-03 22:01:47 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:01:47 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:02:20 [INFO]: [AUTH] User login attempt {"service":"ebbinghaus-backend","environment":"development","identifier":"<EMAIL>"}
2025-08-03 22:02:20 [INFO]: [AUTH] Access token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser"}
2025-08-03 22:02:20 [INFO]: [AUTH] Refresh token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"dc11c2eb-8895-480b-908b-0e522d20bd37"}
2025-08-03 22:02:20 [INFO]: [AUTH] User logged in successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser","email":"<EMAIL>"}
2025-08-03 22:02:20 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/login","method":"POST","statusCode":200,"responseTime":"291ms","requestId":"564dcf42-ddca-42d7-98c1-b164c9fa8131"}
2025-08-03 22:02:20 [INFO]: [AUTH] Refresh token stored {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"dc11c2eb-8895-480b-908b-0e522d20bd37","ttl":2592000}
2025-08-03 22:02:20 [INFO]: [AUTH] Token verified successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","type":"access"}
2025-08-03 22:02:20 [INFO]: [AUTH] Token authenticated successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","path":"/","method":"GET"}
2025-08-03 22:02:20 [INFO]: [API] Getting user tasks {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","page":1,"limit":20,"filters":{}}
2025-08-03 22:02:20 [INFO]: [BUSINESS] User tasks retrieved {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","totalCount":0,"page":1,"limit":20}
2025-08-03 22:02:20 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":200,"responseTime":"48ms","userId":"13289601-6713-4725-a516-f9d1f7886a4d","requestId":"7bd9dc99-df9b-43aa-aadd-0e03eafaf5a6"}
2025-08-03 22:02:35 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"development","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.5737","path":"/"}
2025-08-03 22:02:35 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"POST","statusCode":401,"responseTime":"1ms","requestId":"54740f4b-f83d-471a-8264-3c2717d6f847"}
2025-08-03 22:02:49 [INFO]: [AUTH] User login attempt {"service":"ebbinghaus-backend","environment":"development","identifier":"<EMAIL>"}
2025-08-03 22:02:49 [INFO]: [AUTH] Access token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser"}
2025-08-03 22:02:49 [INFO]: [AUTH] Refresh token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"871d2875-384c-4d13-ad96-d25bf31edbe8"}
2025-08-03 22:02:49 [INFO]: [AUTH] User logged in successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser","email":"<EMAIL>"}
2025-08-03 22:02:49 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/login","method":"POST","statusCode":200,"responseTime":"252ms","requestId":"1c20e846-b366-4b45-a8bb-9b7aac7f6e18"}
2025-08-03 22:02:49 [INFO]: [AUTH] Refresh token stored {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"871d2875-384c-4d13-ad96-d25bf31edbe8","ttl":2592000}
2025-08-03 22:02:49 [INFO]: [AUTH] Token verified successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","type":"access"}
2025-08-03 22:02:49 [INFO]: [AUTH] Token authenticated successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","path":"/","method":"POST"}
2025-08-03 22:02:49 [INFO]: [API] Validation failed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"POST","errors":[{"type":"field","value":null,"msg":"Audio must be a valid URL","path":"content.audio","location":"body"},{"type":"field","value":"programming","msg":"Invalid subject","path":"metadata.subject","location":"body"}],"userId":"13289601-6713-4725-a516-f9d1f7886a4d"}
2025-08-03 22:02:49 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"POST","statusCode":400,"responseTime":"6ms","userId":"13289601-6713-4725-a516-f9d1f7886a4d","requestId":"48579471-b8b1-4d41-9a72-a44cf3ae2b20"}
2025-08-03 22:03:18 [INFO]: [AUTH] User login attempt {"service":"ebbinghaus-backend","environment":"development","identifier":"<EMAIL>"}
2025-08-03 22:03:18 [INFO]: [AUTH] Access token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser"}
2025-08-03 22:03:18 [INFO]: [AUTH] Refresh token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"e2989a78-c370-4657-8ac9-2d1cb35047ba"}
2025-08-03 22:03:18 [INFO]: [AUTH] User logged in successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser","email":"<EMAIL>"}
2025-08-03 22:03:18 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/login","method":"POST","statusCode":200,"responseTime":"256ms","requestId":"c3abe3e7-dfaa-44b7-8c8b-cf74d2465c0e"}
2025-08-03 22:03:18 [INFO]: [AUTH] Refresh token stored {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"e2989a78-c370-4657-8ac9-2d1cb35047ba","ttl":2592000}
2025-08-03 22:03:18 [INFO]: [AUTH] Token verified successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","type":"access"}
2025-08-03 22:03:18 [INFO]: [AUTH] Token authenticated successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","path":"/","method":"POST"}
2025-08-03 22:03:18 [INFO]: [API] Creating task {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","subject":"math","estimatedTime":120}
2025-08-03 22:03:18 [INFO]: [BUSINESS] Task created successfully {"service":"ebbinghaus-backend","environment":"development","taskId":"59326009-7ee2-48e3-9f36-3fafc27ff02c","userId":"13289601-6713-4725-a516-f9d1f7886a4d","subject":"math","estimatedTime":120}
2025-08-03 22:03:18 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"POST","statusCode":201,"responseTime":"18ms","userId":"13289601-6713-4725-a516-f9d1f7886a4d","requestId":"bd9158d3-6580-4ba8-9eb4-3ba49d0af47f"}
2025-08-03 22:03:30 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"development","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.5737","path":"/"}
2025-08-03 22:03:30 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":401,"responseTime":"1ms","requestId":"032c1f01-9674-4db9-b2d7-4c5fba430664"}
2025-08-03 22:03:47 [INFO]: [AUTH] User login attempt {"service":"ebbinghaus-backend","environment":"development","identifier":"<EMAIL>"}
2025-08-03 22:03:47 [INFO]: [AUTH] Access token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser"}
2025-08-03 22:03:47 [INFO]: [AUTH] Refresh token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"d5f9bc03-a0a3-48ab-b757-db7284105318"}
2025-08-03 22:03:47 [INFO]: [AUTH] User logged in successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser","email":"<EMAIL>"}
2025-08-03 22:03:47 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/login","method":"POST","statusCode":200,"responseTime":"251ms","requestId":"0efdcbf5-bc6e-4eff-8245-d54179252e9e"}
2025-08-03 22:03:47 [INFO]: [AUTH] Refresh token stored {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"d5f9bc03-a0a3-48ab-b757-db7284105318","ttl":2592000}
2025-08-03 22:03:47 [INFO]: [AUTH] Token verified successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","type":"access"}
2025-08-03 22:03:47 [INFO]: [AUTH] Token authenticated successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","path":"/","method":"GET"}
2025-08-03 22:03:47 [INFO]: [API] Getting user tasks {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","page":1,"limit":20,"filters":{}}
2025-08-03 22:03:47 [INFO]: [BUSINESS] User tasks retrieved {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","totalCount":1,"page":1,"limit":20}
2025-08-03 22:03:47 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":200,"responseTime":"7ms","userId":"13289601-6713-4725-a516-f9d1f7886a4d","requestId":"d1d1c2f7-985a-46d1-b8df-4ee3b92d36bb"}
2025-08-03 22:09:43 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:09:43 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:09:43 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:09:43 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:09:43 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:09:43 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 22:09:43 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:09:43 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:09:43 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:09:43 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":22608}
2025-08-03 22:09:43 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:09:43 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:14:09 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:14:09 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:14:09 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:14:09 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:14:09 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:14:09 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 22:14:09 [ERROR]: Redis connection failed {"service":"ebbinghaus-backend","environment":"development","error":"Stream isn't writeable and enableOfflineQueue options is false","host":"localhost","port":6379}
2025-08-03 22:14:09 [WARN]: Application will continue without Redis cache {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:14:09 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:14:09 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":21508}
2025-08-03 22:14:09 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:14:09 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:15:43 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/health","method":"GET","statusCode":200,"responseTime":"2ms","requestId":"da1a372a-3523-4898-b313-6e364c1c95f6"}
2025-08-03 22:15:55 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":301,"responseTime":"2ms","requestId":"eef21a64-3515-47a1-a277-4082a4de4527"}
2025-08-03 22:15:55 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":200,"responseTime":"1ms","requestId":"e4e0be01-849f-47a1-88b0-d364d8c01e9f"}
2025-08-03 22:16:08 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"development","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.5737","path":"/"}
2025-08-03 22:16:08 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":401,"responseTime":"2ms","requestId":"73b2d84d-4aea-4154-8ed4-3782a7bd9bde"}
2025-08-03 22:16:47 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/health","method":"GET","statusCode":200,"responseTime":"0ms","requestId":"cd39ea65-5d66-418b-83a2-139089dd0069"}
2025-08-03 22:16:47 [INFO]: [AUTH] User login attempt {"service":"ebbinghaus-backend","environment":"development","identifier":"<EMAIL>"}
2025-08-03 22:16:47 [INFO]: [AUTH] Access token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser"}
2025-08-03 22:16:47 [INFO]: [AUTH] Refresh token generated {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"1c00117b-393e-47e7-a3eb-d808a4121769"}
2025-08-03 22:16:47 [INFO]: [AUTH] User logged in successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","username":"testuser","email":"<EMAIL>"}
2025-08-03 22:16:47 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/login","method":"POST","statusCode":200,"responseTime":"301ms","requestId":"7aacad96-224d-4379-b4a2-6d72e7bae3d7"}
2025-08-03 22:16:47 [INFO]: [AUTH] Refresh token stored {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","tokenId":"1c00117b-393e-47e7-a3eb-d808a4121769","ttl":2592000}
2025-08-03 22:16:47 [INFO]: [AUTH] Token verified successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","type":"access"}
2025-08-03 22:16:47 [INFO]: [AUTH] Token authenticated successfully {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","path":"/","method":"GET"}
2025-08-03 22:16:47 [INFO]: [API] Getting user tasks {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","page":1,"limit":20,"filters":{}}
2025-08-03 22:16:47 [INFO]: [BUSINESS] User tasks retrieved {"service":"ebbinghaus-backend","environment":"development","userId":"13289601-6713-4725-a516-f9d1f7886a4d","totalCount":1,"page":1,"limit":20}
2025-08-03 22:16:47 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":200,"responseTime":"11ms","userId":"13289601-6713-4725-a516-f9d1f7886a4d","requestId":"0b676ff4-1c05-4c62-a6b2-adb82c1e2a31"}
2025-08-03 22:16:47 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":301,"responseTime":"1ms","requestId":"fc1d5e25-f98d-43ac-9ecf-8ee0d275ddb5"}
2025-08-03 22:16:47 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":200,"responseTime":"1ms","requestId":"c771250f-de40-43d2-89bd-b168b00843b1"}
2025-08-03 22:20:42 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/health","method":"GET","statusCode":200,"responseTime":"1ms","requestId":"842ba082-d0f7-4cc9-a890-94b24f98d219"}
2025-08-03 22:20:42 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":301,"responseTime":"1ms","requestId":"5b687d94-e4af-41e7-b26b-48edadf8361f"}
2025-08-03 22:20:42 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":200,"responseTime":"1ms","requestId":"ff21eccb-5ebd-461e-a1c4-f891f158dbea"}
2025-08-03 22:20:42 [WARN]: [SECURITY] Missing authentication token {"service":"ebbinghaus-backend","environment":"development","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; zh-CN) WindowsPowerShell/5.1.19041.5737","path":"/"}
2025-08-03 22:20:42 [INFO]: [PERFORMANCE] Request completed {"service":"ebbinghaus-backend","environment":"development","path":"/","method":"GET","statusCode":401,"responseTime":"1ms","requestId":"9033c54d-b38c-499c-b4b6-223352fba221"}
2025-08-03 22:21:57 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:21:57 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:21:57 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:21:57 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:22:34 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:22:34 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:22:34 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:22:34 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:23:14 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:23:14 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:23:14 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:23:14 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:23:14 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:23:14 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 22:23:14 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:23:14 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:23:14 [INFO]: Redis connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0,"status":"ready"}
2025-08-03 22:23:14 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:23:14 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3002","nodeVersion":"v22.14.0","pid":23424}
2025-08-03 22:23:40 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:23:40 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:23:40 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:23:40 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:23:40 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:23:40 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 22:23:40 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:02 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:02 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:02 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:25:02 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:25:02 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:02 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 22:25:02 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:02 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:02 [INFO]: Redis connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0,"status":"ready"}
2025-08-03 22:25:02 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:02 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3003","nodeVersion":"v22.14.0","pid":27248}
2025-08-03 22:25:44 [INFO]: Initializing services... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:44 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:44 [INFO]: Connecting to MongoDB... {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning"}
2025-08-03 22:25:44 [INFO]: MongoDB connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost:27017","database":"/localhost:27017/ebbinghaus_learning","readyState":1}
2025-08-03 22:25:44 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:44 [INFO]: Connecting to Redis... {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0}
2025-08-03 22:25:44 [INFO]: Redis client connected {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:44 [INFO]: Redis client ready {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:44 [INFO]: Redis connected successfully {"service":"ebbinghaus-backend","environment":"development","host":"localhost","port":6379,"db":0,"status":"ready"}
2025-08-03 22:25:44 [INFO]: All services initialized successfully {"service":"ebbinghaus-backend","environment":"development"}
2025-08-03 22:25:44 [INFO]: 🚀 Server started successfully {"service":"ebbinghaus-backend","environment":"development","port":"3003","nodeVersion":"v22.14.0","pid":23368}
