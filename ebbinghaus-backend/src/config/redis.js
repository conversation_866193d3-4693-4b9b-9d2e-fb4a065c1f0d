// src/config/redis.js
const Redis = require('ioredis');
const logger = require('./logger');

class RedisConfig {
  constructor() {
    this.client = null;
    this.isConnected = false;
    this.connectionOptions = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      db: parseInt(process.env.REDIS_DB) || 0,
      
      // 连接选项
      connectTimeout: 10000, // 连接超时时间
      commandTimeout: 5000,  // 命令超时时间
      retryDelayOnFailover: 100, // 故障转移重试延迟
      maxRetriesPerRequest: 3, // 每个请求最大重试次数
      
      // 重连策略
      retryDelayOnClusterDown: 300,
      enableOfflineQueue: true,
      
      // 键名前缀
      keyPrefix: 'ebbinghaus:',
      
      // 序列化选项
      lazyConnect: true, // 延迟连接
      keepAlive: 30000,  // 保持连接时间
      
      // 集群模式（如果需要）
      enableReadyCheck: true,
      maxLoadingTimeout: 5000
    };
  }

  /**
   * 连接到Redis服务器
   * @returns {Promise<void>}
   */
  async connect() {
    try {
      if (this.isConnected && this.client) {
        logger.info('Redis already connected');
        return this.client;
      }

      logger.info('Connecting to Redis...', {
        host: this.connectionOptions.host,
        port: this.connectionOptions.port,
        db: this.connectionOptions.db
      });

      this.client = new Redis(this.connectionOptions);
      
      // 设置事件监听器
      this.setupEventListeners();
      
      // 测试连接
      await this.client.ping();
      
      this.isConnected = true;
      
      logger.info('Redis connected successfully', {
        host: this.connectionOptions.host,
        port: this.connectionOptions.port,
        db: this.connectionOptions.db,
        status: this.client.status
      });

      return this.client;
    } catch (error) {
      this.isConnected = false;
      logger.error('Redis connection failed', {
        error: error.message,
        host: this.connectionOptions.host,
        port: this.connectionOptions.port
      });
      
      // Redis连接失败不应该阻止应用启动
      logger.warn('Application will continue without Redis cache');
      return null;
    }
  }

  /**
   * 断开Redis连接
   * @returns {Promise<void>}
   */
  async disconnect() {
    try {
      if (!this.client || !this.isConnected) {
        logger.info('Redis already disconnected');
        return;
      }

      await this.client.quit();
      this.client = null;
      this.isConnected = false;
      
      logger.info('Redis disconnected successfully');
    } catch (error) {
      logger.error('Error disconnecting from Redis', {
        error: error.message
      });
    }
  }

  /**
   * 获取Redis客户端实例
   * @returns {Redis|null} Redis客户端实例
   */
  getClient() {
    return this.client;
  }

  /**
   * 检查Redis连接状态
   * @returns {boolean} 连接状态
   */
  isHealthy() {
    return this.isConnected && this.client && this.client.status === 'ready';
  }

  /**
   * 获取Redis连接信息
   * @returns {Object} 连接信息
   */
  getConnectionInfo() {
    return {
      isConnected: this.isConnected,
      status: this.client ? this.client.status : 'disconnected',
      host: this.connectionOptions.host,
      port: this.connectionOptions.port,
      db: this.connectionOptions.db,
      keyPrefix: this.connectionOptions.keyPrefix
    };
  }

  /**
   * 设置缓存
   * @param {string} key - 键名
   * @param {any} value - 值
   * @param {number} ttl - 过期时间（秒）
   * @returns {Promise<string|null>} 设置结果
   */
  async set(key, value, ttl = 3600) {
    try {
      if (!this.isHealthy()) {
        logger.warn('Redis not available, skipping cache set', { key });
        return null;
      }

      const serializedValue = JSON.stringify(value);
      
      if (ttl > 0) {
        return await this.client.setex(key, ttl, serializedValue);
      } else {
        return await this.client.set(key, serializedValue);
      }
    } catch (error) {
      logger.error('Redis set operation failed', {
        key,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 获取缓存
   * @param {string} key - 键名
   * @returns {Promise<any|null>} 缓存值
   */
  async get(key) {
    try {
      if (!this.isHealthy()) {
        logger.warn('Redis not available, skipping cache get', { key });
        return null;
      }

      const value = await this.client.get(key);
      
      if (value === null) {
        return null;
      }

      return JSON.parse(value);
    } catch (error) {
      logger.error('Redis get operation failed', {
        key,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 删除缓存
   * @param {string} key - 键名
   * @returns {Promise<number|null>} 删除的键数量
   */
  async del(key) {
    try {
      if (!this.isHealthy()) {
        logger.warn('Redis not available, skipping cache delete', { key });
        return null;
      }

      return await this.client.del(key);
    } catch (error) {
      logger.error('Redis delete operation failed', {
        key,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 检查键是否存在
   * @param {string} key - 键名
   * @returns {Promise<boolean>} 是否存在
   */
  async exists(key) {
    try {
      if (!this.isHealthy()) {
        return false;
      }

      const result = await this.client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Redis exists operation failed', {
        key,
        error: error.message
      });
      return false;
    }
  }

  /**
   * 设置键的过期时间
   * @param {string} key - 键名
   * @param {number} ttl - 过期时间（秒）
   * @returns {Promise<number|null>} 设置结果
   */
  async expire(key, ttl) {
    try {
      if (!this.isHealthy()) {
        return null;
      }

      return await this.client.expire(key, ttl);
    } catch (error) {
      logger.error('Redis expire operation failed', {
        key,
        ttl,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 清空当前数据库（仅用于测试环境）
   * @returns {Promise<void>}
   */
  async flushdb() {
    if (process.env.NODE_ENV !== 'test') {
      throw new Error('Database flushing is only allowed in test environment');
    }

    try {
      if (!this.isHealthy()) {
        return;
      }

      await this.client.flushdb();
      logger.info('Redis test database flushed successfully');
    } catch (error) {
      logger.error('Error flushing Redis test database', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * 设置事件监听器
   * @private
   */
  setupEventListeners() {
    if (!this.client) return;

    // 连接成功
    this.client.on('connect', () => {
      logger.info('Redis client connected');
    });

    // 准备就绪
    this.client.on('ready', () => {
      this.isConnected = true;
      logger.info('Redis client ready');
    });

    // 连接错误
    this.client.on('error', (error) => {
      this.isConnected = false;
      logger.error('Redis client error', {
        error: error.message
      });
    });

    // 连接关闭
    this.client.on('close', () => {
      this.isConnected = false;
      logger.warn('Redis client connection closed');
    });

    // 重连
    this.client.on('reconnecting', (delay) => {
      logger.info('Redis client reconnecting', { delay });
    });

    // 应用终止时断开连接
    process.on('SIGINT', async () => {
      try {
        await this.disconnect();
        logger.info('Redis connection closed through app termination');
      } catch (error) {
        logger.error('Error closing Redis connection on app termination', {
          error: error.message
        });
      }
    });
  }
}

// 创建单例实例
const redisConfig = new RedisConfig();

module.exports = redisConfig;
