// src/middleware/errorHandler.js
const { HTTP_STATUS, ERROR_CODES } = require('../utils/constants');
const logger = require('../config/logger');

/**
 * 全局错误处理中间件
 * 捕获并处理应用中的所有错误
 */
const globalErrorHandler = (err, req, res, next) => {
  // 记录错误日志
  logger.error('Global error handler caught error', {
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    userId: req.user?.userId,
    requestId: req.requestId,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // 默认错误响应
  let statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
  let errorCode = ERROR_CODES.SERVER_ERROR;
  let message = 'Internal server error';
  let details = null;

  // 根据错误类型设置响应
  if (err.name === 'ValidationError') {
    // Mongoose验证错误
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = ERROR_CODES.VALIDATION_ERROR;
    message = 'Validation failed';
    details = Object.values(err.errors).map(e => e.message);
  } else if (err.name === 'CastError') {
    // Mongoose类型转换错误
    statusCode = HTTP_STATUS.BAD_REQUEST;
    errorCode = ERROR_CODES.INVALID_INPUT;
    message = 'Invalid data format';
  } else if (err.code === 11000) {
    // MongoDB重复键错误
    statusCode = HTTP_STATUS.CONFLICT;
    errorCode = ERROR_CODES.DUPLICATE_KEY;
    message = 'Duplicate entry';
    
    // 提取重复的字段
    const field = Object.keys(err.keyValue)[0];
    details = `${field} already exists`;
  } else if (err.name === 'JsonWebTokenError') {
    // JWT错误
    statusCode = HTTP_STATUS.UNAUTHORIZED;
    errorCode = ERROR_CODES.TOKEN_INVALID;
    message = 'Invalid token';
  } else if (err.name === 'TokenExpiredError') {
    // JWT过期错误
    statusCode = HTTP_STATUS.UNAUTHORIZED;
    errorCode = ERROR_CODES.TOKEN_EXPIRED;
    message = 'Token expired';
  } else if (err.name === 'MongoError' || err.name === 'MongoServerError') {
    // MongoDB错误
    statusCode = HTTP_STATUS.INTERNAL_SERVER_ERROR;
    errorCode = ERROR_CODES.DATABASE_ERROR;
    message = 'Database error';
  } else if (err.message && Object.values(ERROR_CODES).includes(err.message)) {
    // 自定义错误代码
    errorCode = err.message;
    
    // 根据错误代码设置状态码和消息
    switch (errorCode) {
      case ERROR_CODES.TASK_NOT_FOUND:
      case ERROR_CODES.USER_NOT_FOUND:
      case ERROR_CODES.REVIEW_NOT_FOUND:
        statusCode = HTTP_STATUS.NOT_FOUND;
        message = 'Resource not found';
        break;
      case ERROR_CODES.INVALID_CREDENTIALS:
        statusCode = HTTP_STATUS.UNAUTHORIZED;
        message = 'Invalid credentials';
        break;
      case ERROR_CODES.UNAUTHORIZED:
        statusCode = HTTP_STATUS.UNAUTHORIZED;
        message = 'Unauthorized access';
        break;
      case ERROR_CODES.FORBIDDEN:
        statusCode = HTTP_STATUS.FORBIDDEN;
        message = 'Access forbidden';
        break;
      case ERROR_CODES.USER_EXISTS:
        statusCode = HTTP_STATUS.CONFLICT;
        message = 'User already exists';
        break;
      case ERROR_CODES.INVALID_INPUT:
      case ERROR_CODES.VALIDATION_ERROR:
        statusCode = HTTP_STATUS.BAD_REQUEST;
        message = 'Invalid input';
        break;
      default:
        message = err.message;
    }
  } else if (err.statusCode) {
    // 自定义状态码错误
    statusCode = err.statusCode;
    message = err.message;
  }

  // 在开发环境中包含错误堆栈
  const response = {
    success: false,
    message,
    code: errorCode,
    ...(details && { details }),
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  };

  res.status(statusCode).json(response);
};

/**
 * 404错误处理中间件
 * 处理未找到的路由
 */
const notFoundHandler = (req, res, next) => {
  logger.api('Route not found', {
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  res.status(HTTP_STATUS.NOT_FOUND).json({
    success: false,
    message: 'Route not found',
    code: ERROR_CODES.NOT_FOUND,
    path: req.path,
    method: req.method
  });
};

/**
 * 异步错误包装器
 * 包装异步路由处理器以捕获Promise拒绝
 */
const asyncHandler = (fn) => {
  return (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * 请求超时中间件
 */
const timeoutHandler = (timeout = 30000) => {
  return (req, res, next) => {
    // 设置请求超时
    req.setTimeout(timeout, () => {
      logger.error('Request timeout', {
        path: req.path,
        method: req.method,
        timeout,
        userId: req.user?.userId,
        requestId: req.requestId
      });

      if (!res.headersSent) {
        res.status(HTTP_STATUS.SERVICE_UNAVAILABLE).json({
          success: false,
          message: 'Request timeout',
          code: ERROR_CODES.SERVER_ERROR
        });
      }
    });

    next();
  };
};

/**
 * 内存使用监控中间件
 */
const memoryMonitor = (req, res, next) => {
  const memUsage = process.memoryUsage();
  const memUsageMB = {
    rss: Math.round(memUsage.rss / 1024 / 1024),
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
    external: Math.round(memUsage.external / 1024 / 1024)
  };

  // 如果内存使用过高，记录警告
  if (memUsageMB.heapUsed > 500) { // 500MB
    logger.performance('High memory usage detected', {
      memoryUsage: memUsageMB,
      path: req.path,
      method: req.method
    });
  }

  // 将内存使用信息添加到响应头（仅在开发环境）
  if (process.env.NODE_ENV === 'development') {
    res.setHeader('X-Memory-Usage', JSON.stringify(memUsageMB));
  }

  next();
};

/**
 * 响应时间监控中间件
 */
const responseTimeMonitor = (req, res, next) => {
  const startTime = Date.now();

  // 重写res.end方法来在响应发送前设置头部
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;

    // 在响应发送前设置响应时间头部
    if (!res.headersSent) {
      res.setHeader('X-Response-Time', `${responseTime}ms`);
    }

    // 记录响应时间
    logger.performance('Request completed', {
      path: req.path,
      method: req.method,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userId: req.user?.userId,
      requestId: req.requestId
    });

    // 如果响应时间过长，记录警告
    if (responseTime > 5000) { // 5秒
      logger.performance('Slow response detected', {
        path: req.path,
        method: req.method,
        responseTime: `${responseTime}ms`,
        userId: req.user?.userId
      });
    }

    // 调用原始的end方法
    originalEnd.apply(this, args);
  };

  next();
};

/**
 * 健康检查错误处理
 */
const healthCheckErrorHandler = (req, res, next) => {
  // 对于健康检查路由，使用简化的错误处理
  if (req.path === '/health' || req.path === '/api/health') {
    return (err, req, res, next) => {
      logger.error('Health check error', {
        error: err.message,
        stack: err.stack
      });

      res.status(HTTP_STATUS.SERVICE_UNAVAILABLE).json({
        status: 'unhealthy',
        error: err.message,
        timestamp: new Date().toISOString()
      });
    };
  }
  
  next();
};

/**
 * 错误恢复中间件
 * 尝试从某些错误中恢复
 */
const errorRecovery = (err, req, res, next) => {
  // 数据库连接错误恢复
  if (err.name === 'MongoNetworkError' || err.name === 'MongoTimeoutError') {
    logger.error('Database connection error, attempting recovery', {
      error: err.message,
      path: req.path
    });

    // 这里可以添加数据库重连逻辑
    // 暂时直接传递给下一个错误处理器
  }

  // Redis连接错误恢复
  if (err.message && err.message.includes('Redis')) {
    logger.error('Redis connection error, continuing without cache', {
      error: err.message,
      path: req.path
    });

    // Redis错误不应该阻止请求，继续处理
    return next();
  }

  // 传递给下一个错误处理器
  next(err);
};

/**
 * 开发环境错误处理器
 */
const developmentErrorHandler = (err, req, res, next) => {
  if (process.env.NODE_ENV !== 'development') {
    return next(err);
  }

  // 在开发环境中提供更详细的错误信息
  const detailedError = {
    success: false,
    message: err.message,
    code: err.code || ERROR_CODES.SERVER_ERROR,
    stack: err.stack,
    details: {
      name: err.name,
      path: req.path,
      method: req.method,
      body: req.body,
      params: req.params,
      query: req.query
    }
  };

  res.status(err.statusCode || HTTP_STATUS.INTERNAL_SERVER_ERROR).json(detailedError);
};

module.exports = {
  globalErrorHandler,
  notFoundHandler,
  asyncHandler,
  timeoutHandler,
  memoryMonitor,
  responseTimeMonitor,
  healthCheckErrorHandler,
  errorRecovery,
  developmentErrorHandler
};
