// src/models/Task.js
const mongoose = require('mongoose');
const { 
  SUBJECTS, 
  TASK_STATUS, 
  PRIORITY_LEVELS, 
  DIFFICULTY_LEVELS 
} = require('../utils/constants');

const taskSchema = new mongoose.Schema({
  taskId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  userId: {
    type: String,
    ref: 'User',
    required: true,
    index: true
  },
  title: {
    type: String,
    required: true,
    maxlength: 100,
    trim: true
  },
  content: {
    text: {
      type: String,
      required: true,
      maxlength: 5000
    },
    images: [{
      type: String // 图片URL
    }],
    audio: {
      type: String // 音频URL
    },
    attachments: [{
      type: String // 附件URL
    }]
  },
  metadata: {
    subject: {
      type: String,
      required: true,
      enum: Object.values(SUBJECTS)
    },
    estimatedTime: {
      type: Number,
      required: true,
      min: 1,
      max: 300 // 最大5小时
    },
    actualTime: {
      type: Number,
      min: 0
    },
    priority: {
      type: Number,
      required: true,
      min: PRIORITY_LEVELS.VERY_LOW,
      max: PRIORITY_LEVELS.VERY_HIGH,
      default: PRIORITY_LEVELS.MEDIUM
    },
    difficulty: {
      type: Number,
      required: true,
      min: DIFFICULTY_LEVELS.VERY_EASY,
      max: DIFFICULTY_LEVELS.VERY_HARD,
      default: DIFFICULTY_LEVELS.MEDIUM
    },
    tags: [{
      type: String,
      trim: true
    }]
  },
  status: {
    type: String,
    enum: Object.values(TASK_STATUS),
    default: TASK_STATUS.PENDING,
    index: true
  },
  progress: {
    completedAt: Date,
    startedAt: Date,
    notes: {
      type: String,
      maxlength: 1000
    }
  },
  // 搜索文本字段
  searchText: {
    type: String,
    index: 'text'
  },
  // 关联信息
  mindMapId: {
    type: String,
    index: true
  },
  parentTaskId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Task',
    index: true
  }
}, {
  timestamps: true, // 自动添加createdAt和updatedAt
  collection: 'tasks'
});

// 复合索引
taskSchema.index({ userId: 1, status: 1, createdAt: -1 });
taskSchema.index({ userId: 1, 'metadata.subject': 1, createdAt: -1 });
taskSchema.index({ userId: 1, 'metadata.priority': 1, createdAt: -1 });

// 中间件：保存前更新搜索文本
taskSchema.pre('save', function(next) {
  this.searchText = `${this.title} ${this.content.text}`.toLowerCase();
  next();
});

// 虚拟字段：任务完成率
taskSchema.virtual('completionRate').get(function() {
  if (this.status === TASK_STATUS.COMPLETED) return 100;
  if (this.status === TASK_STATUS.IN_PROGRESS) return 50;
  return 0;
});

// 实例方法：更新任务状态
taskSchema.methods.updateStatus = function(newStatus, notes = '') {
  this.status = newStatus;

  if (newStatus === TASK_STATUS.IN_PROGRESS && !this.progress.startedAt) {
    this.progress.startedAt = new Date();
  } else if (newStatus === TASK_STATUS.COMPLETED) {
    this.progress.completedAt = new Date();
    if (this.progress.startedAt) {
      this.metadata.actualTime = Math.round(
        (this.progress.completedAt - this.progress.startedAt) / (1000 * 60)
      );
    }
  }

  if (notes) {
    this.progress.notes = notes;
  }

  return this.save();
};

// 实例方法：获取安全的任务信息（用于API响应）
taskSchema.methods.toSafeObject = function() {
  const taskObject = this.toObject();
  // 移除敏感信息或内部字段
  delete taskObject.searchText;
  return taskObject;
};

// 静态方法：根据用户ID查找任务
taskSchema.statics.findByUserId = function(userId, options = {}) {
  const query = { userId };
  
  if (options.status) {
    query.status = options.status;
  }
  
  if (options.subject) {
    query['metadata.subject'] = options.subject;
  }
  
  return this.find(query);
};

// 静态方法：获取用户任务统计
taskSchema.statics.getUserTaskStats = async function(userId) {
  const stats = await this.aggregate([
    { $match: { userId: new mongoose.Types.ObjectId(userId) } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalEstimatedTime: { $sum: '$metadata.estimatedTime' },
        totalActualTime: { $sum: '$metadata.actualTime' }
      }
    }
  ]);
  
  const result = {
    total: 0,
    pending: 0,
    inProgress: 0,
    completed: 0,
    cancelled: 0,
    totalEstimatedTime: 0,
    totalActualTime: 0
  };
  
  stats.forEach(stat => {
    result.total += stat.count;
    result.totalEstimatedTime += stat.totalEstimatedTime || 0;
    result.totalActualTime += stat.totalActualTime || 0;
    
    switch (stat._id) {
      case TASK_STATUS.PENDING:
        result.pending = stat.count;
        break;
      case TASK_STATUS.IN_PROGRESS:
        result.inProgress = stat.count;
        break;
      case TASK_STATUS.COMPLETED:
        result.completed = stat.count;
        break;
      case TASK_STATUS.CANCELLED:
        result.cancelled = stat.count;
        break;
    }
  });
  
  return result;
};

// 静态方法：搜索任务
taskSchema.statics.searchTasks = function(userId, searchQuery, options = {}) {
  const query = {
    userId,
    $text: { $search: searchQuery }
  };
  
  if (options.status) {
    query.status = options.status;
  }
  
  if (options.subject) {
    query['metadata.subject'] = options.subject;
  }
  
  return this.find(query, { score: { $meta: 'textScore' } })
    .sort({ score: { $meta: 'textScore' } });
};

module.exports = mongoose.model('Task', taskSchema);
