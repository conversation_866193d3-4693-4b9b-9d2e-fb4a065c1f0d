// src/models/User.js
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const { 
  SUBJECTS, 
  PRIORITY_LEVELS, 
  DIFFICULTY_LEVELS,
  DEFAULT_CONFIG,
  REGEX_PATTERNS 
} = require('../utils/constants');

const userSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    unique: true
  },
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 50,
    match: [REGEX_PATTERNS.USERNAME, '用户名格式不正确']
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [REGEX_PATTERNS.EMAIL, '请输入有效的邮箱地址']
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  nickname: {
    type: String,
    trim: true,
    maxlength: 50
  },
  avatar: {
    type: String // 头像URL
  },
  preferences: {
    defaultSubject: {
      type: String,
      enum: Object.values(SUBJECTS)
    },
    defaultEstimatedTime: {
      type: Number,
      default: DEFAULT_CONFIG.DEFAULT_ESTIMATED_TIME,
      min: 1,
      max: 300
    },
    defaultPriority: {
      type: Number,
      default: DEFAULT_CONFIG.DEFAULT_PRIORITY,
      min: PRIORITY_LEVELS.VERY_LOW,
      max: PRIORITY_LEVELS.VERY_HIGH
    },
    defaultDifficulty: {
      type: Number,
      default: DEFAULT_CONFIG.DEFAULT_DIFFICULTY,
      min: DIFFICULTY_LEVELS.VERY_EASY,
      max: DIFFICULTY_LEVELS.VERY_HARD
    },
    dailyStudyLimit: {
      type: Number,
      default: DEFAULT_CONFIG.DEFAULT_DAILY_STUDY_LIMIT,
      min: 30,
      max: 600
    },
    reviewPreferences: {
      preferredTimes: [{
        type: String,
        match: [REGEX_PATTERNS.TIME_FORMAT, '时间格式应为HH:mm']
      }],
      maxDailyReviews: {
        type: Number,
        default: DEFAULT_CONFIG.DEFAULT_MAX_DAILY_REVIEWS,
        min: 1,
        max: 50
      },
      reviewInterval: {
        type: String,
        enum: ['strict', 'flexible'],
        default: 'flexible'
      },
      restHours: [{
        type: String,
        match: [REGEX_PATTERNS.REST_HOURS, '休息时间格式应为HH-HH']
      }]
    },
    theme: {
      type: String,
      enum: ['light', 'dark', 'auto'],
      default: 'light'
    },
    language: {
      type: String,
      default: 'zh-CN'
    },
    timezone: {
      type: String,
      default: 'Asia/Shanghai'
    }
  },
  statistics: {
    totalTasks: {
      type: Number,
      default: 0
    },
    completedTasks: {
      type: Number,
      default: 0
    },
    totalStudyTime: {
      type: Number,
      default: 0 // 总学习时间（分钟）
    },
    totalReviewTime: {
      type: Number,
      default: 0 // 总复习时间（分钟）
    },
    averageEffectiveness: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    currentStreak: {
      type: Number,
      default: 0 // 当前连续学习天数
    },
    longestStreak: {
      type: Number,
      default: 0 // 最长连续学习天数
    },
    joinedAt: {
      type: Date,
      default: Date.now
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLoginAt: Date,
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: String,
  passwordResetToken: String,
  passwordResetExpires: Date
}, {
  timestamps: true,
  collection: 'users'
});

// 复合索引（避免重复单字段索引，因为字段定义中已设置index: true）
userSchema.index({ email: 1, emailVerified: 1 });
userSchema.index({ userId: 1, isActive: 1 });
userSchema.index({ username: 1, isActive: 1 });

// 中间件：保存前加密密码
userSchema.pre('save', async function(next) {
  // 只有密码被修改时才加密
  if (!this.isModified('password')) return next();

  try {
    // 生成盐值并加密密码
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// 实例方法：验证密码
userSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// 实例方法：更新统计信息
userSchema.methods.updateStatistics = function(updates) {
  Object.keys(updates).forEach(key => {
    if (this.statistics[key] !== undefined) {
      this.statistics[key] = updates[key];
    }
  });
  return this.save();
};

// 实例方法：获取安全的用户信息（不包含密码）
userSchema.methods.toSafeObject = function() {
  const userObject = this.toObject();
  delete userObject.password;
  delete userObject.emailVerificationToken;
  delete userObject.passwordResetToken;
  delete userObject.passwordResetExpires;
  return userObject;
};

// 实例方法：更新最后登录时间
userSchema.methods.updateLastLogin = function() {
  this.lastLoginAt = new Date();
  return this.save();
};

// 实例方法：更新学习连续天数
userSchema.methods.updateStreak = function(studiedToday = true) {
  if (studiedToday) {
    this.statistics.currentStreak += 1;
    if (this.statistics.currentStreak > this.statistics.longestStreak) {
      this.statistics.longestStreak = this.statistics.currentStreak;
    }
  } else {
    this.statistics.currentStreak = 0;
  }
  return this.save();
};

// 虚拟字段：完成率
userSchema.virtual('completionRate').get(function() {
  if (this.statistics.totalTasks === 0) return 0;
  return Math.round((this.statistics.completedTasks / this.statistics.totalTasks) * 100);
});

// 虚拟字段：平均学习时间
userSchema.virtual('averageStudyTime').get(function() {
  if (this.statistics.completedTasks === 0) return 0;
  return Math.round(this.statistics.totalStudyTime / this.statistics.completedTasks);
});

// 静态方法：查找活跃用户
userSchema.statics.findActiveUsers = function() {
  return this.find({ isActive: true });
};

// 静态方法：根据邮箱或用户名查找用户
userSchema.statics.findByEmailOrUsername = function(identifier) {
  return this.findOne({
    $or: [
      { email: identifier.toLowerCase() },
      { username: identifier }
    ]
  });
};

// 静态方法：获取用户学习统计
userSchema.statics.getUserLearningStats = async function(userId, dateRange = null) {
  const user = await this.findById(userId);
  if (!user) return null;

  // 基础统计信息
  const stats = {
    user: user.toSafeObject(),
    basicStats: user.statistics,
    completionRate: user.completionRate,
    averageStudyTime: user.averageStudyTime
  };

  // 如果提供了日期范围，计算该时间段的统计
  if (dateRange) {
    const Task = mongoose.model('Task');
    const ReviewSchedule = mongoose.model('ReviewSchedule');

    const [taskStats, reviewStats] = await Promise.all([
      Task.aggregate([
        {
          $match: {
            userId: new mongoose.Types.ObjectId(userId),
            createdAt: { $gte: dateRange.start, $lte: dateRange.end }
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 },
            totalTime: { $sum: '$metadata.actualTime' }
          }
        }
      ]),
      ReviewSchedule.aggregate([
        {
          $match: {
            userId: new mongoose.Types.ObjectId(userId),
            actualTime: { $gte: dateRange.start, $lte: dateRange.end },
            status: 'completed'
          }
        },
        {
          $group: {
            _id: null,
            count: { $sum: 1 },
            totalDuration: { $sum: '$duration' },
            avgEffectiveness: { $avg: '$effectiveness' }
          }
        }
      ])
    ]);

    stats.periodStats = {
      tasks: taskStats,
      reviews: reviewStats[0] || { count: 0, totalDuration: 0, avgEffectiveness: 0 }
    };
  }

  return stats;
};

module.exports = mongoose.model('User', userSchema);
