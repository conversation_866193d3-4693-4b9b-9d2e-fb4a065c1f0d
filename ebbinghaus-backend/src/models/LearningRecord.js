// src/models/LearningRecord.js
const mongoose = require('mongoose');
const { SUBJECTS, EFFECTIVENESS_SCORES } = require('../utils/constants');

const learningRecordSchema = new mongoose.Schema({
  recordId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  userId: {
    type: String,
    ref: 'User',
    required: true,
    index: true
  },
  taskId: {
    type: String,
    ref: 'Task',
    required: true,
    index: true
  },
  reviewScheduleId: {
    type: String,
    ref: 'ReviewSchedule',
    index: true
  },
  sessionType: {
    type: String,
    enum: ['initial_learning', 'review', 'practice'],
    required: true,
    index: true
  },
  startTime: {
    type: Date,
    required: true,
    index: true
  },
  endTime: {
    type: Date,
    required: true
  },
  duration: {
    type: Number, // 学习时长（分钟）
    required: true,
    min: 1
  },
  effectiveness: {
    type: Number,
    required: true,
    min: EFFECTIVENESS_SCORES.VERY_POOR,
    max: EFFECTIVENESS_SCORES.EXCELLENT
  },
  metadata: {
    subject: {
      type: String,
      required: true,
      enum: Object.values(SUBJECTS)
    },
    difficulty: {
      type: Number,
      required: true,
      min: 1,
      max: 5
    },
    focusLevel: {
      type: Number,
      min: 1,
      max: 5 // 专注度评分
    },
    environment: {
      type: String,
      enum: ['quiet', 'normal', 'noisy'],
      default: 'normal'
    },
    device: {
      type: String,
      enum: ['desktop', 'mobile', 'tablet'],
      default: 'desktop'
    }
  },
  performance: {
    correctAnswers: {
      type: Number,
      min: 0
    },
    totalQuestions: {
      type: Number,
      min: 0
    },
    accuracy: {
      type: Number,
      min: 0,
      max: 100
    },
    speed: {
      type: Number, // 每题平均用时（秒）
      min: 0
    }
  },
  notes: {
    type: String,
    maxlength: 1000
  },
  tags: [{
    type: String,
    trim: true
  }],
  // 学习状态记录
  interruptions: {
    type: Number,
    default: 0 // 中断次数
  },
  pauseDuration: {
    type: Number,
    default: 0 // 暂停总时长（分钟）
  }
}, {
  timestamps: true,
  collection: 'learning_records'
});

// 复合索引
learningRecordSchema.index({ userId: 1, startTime: -1 });
learningRecordSchema.index({ userId: 1, sessionType: 1, startTime: -1 });
learningRecordSchema.index({ userId: 1, 'metadata.subject': 1, startTime: -1 });

// 中间件：保存前计算准确率
learningRecordSchema.pre('save', function(next) {
  if (this.performance.totalQuestions > 0) {
    this.performance.accuracy = Math.round(
      (this.performance.correctAnswers / this.performance.totalQuestions) * 100
    );
  }
  next();
});

// 虚拟字段：学习效率
learningRecordSchema.virtual('efficiency').get(function() {
  // 基于时长、效果和专注度计算学习效率
  const baseScore = this.effectiveness * 20; // 转换为百分制
  const focusBonus = (this.metadata.focusLevel || 3) * 5;
  const interruptionPenalty = this.interruptions * 5;
  
  return Math.max(0, Math.min(100, baseScore + focusBonus - interruptionPenalty));
});

// 虚拟字段：净学习时间
learningRecordSchema.virtual('netLearningTime').get(function() {
  return Math.max(0, this.duration - this.pauseDuration);
});

// 实例方法：更新学习表现
learningRecordSchema.methods.updatePerformance = function(correctAnswers, totalQuestions, avgTimePerQuestion) {
  this.performance.correctAnswers = correctAnswers;
  this.performance.totalQuestions = totalQuestions;
  this.performance.speed = avgTimePerQuestion;
  return this.save();
};

// 静态方法：获取用户学习记录
learningRecordSchema.statics.getUserRecords = function(userId, options = {}) {
  const query = { userId };
  
  if (options.sessionType) {
    query.sessionType = options.sessionType;
  }
  
  if (options.subject) {
    query['metadata.subject'] = options.subject;
  }
  
  if (options.dateRange) {
    query.startTime = {
      $gte: options.dateRange.start,
      $lte: options.dateRange.end
    };
  }
  
  return this.find(query)
    .populate('taskId', 'title metadata.subject')
    .sort({ startTime: -1 });
};

// 静态方法：获取学习统计
learningRecordSchema.statics.getLearningStats = async function(userId, dateRange = null) {
  const matchQuery = { userId: new mongoose.Types.ObjectId(userId) };
  
  if (dateRange) {
    matchQuery.startTime = {
      $gte: dateRange.start,
      $lte: dateRange.end
    };
  }
  
  const stats = await this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: null,
        totalSessions: { $sum: 1 },
        totalDuration: { $sum: '$duration' },
        avgEffectiveness: { $avg: '$effectiveness' },
        avgFocusLevel: { $avg: '$metadata.focusLevel' },
        totalCorrectAnswers: { $sum: '$performance.correctAnswers' },
        totalQuestions: { $sum: '$performance.totalQuestions' },
        avgAccuracy: { $avg: '$performance.accuracy' }
      }
    }
  ]);
  
  const subjectStats = await this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: '$metadata.subject',
        sessions: { $sum: 1 },
        duration: { $sum: '$duration' },
        avgEffectiveness: { $avg: '$effectiveness' }
      }
    },
    { $sort: { duration: -1 } }
  ]);
  
  const sessionTypeStats = await this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: '$sessionType',
        sessions: { $sum: 1 },
        duration: { $sum: '$duration' },
        avgEffectiveness: { $avg: '$effectiveness' }
      }
    }
  ]);
  
  return {
    overall: stats[0] || {
      totalSessions: 0,
      totalDuration: 0,
      avgEffectiveness: 0,
      avgFocusLevel: 0,
      totalCorrectAnswers: 0,
      totalQuestions: 0,
      avgAccuracy: 0
    },
    bySubject: subjectStats,
    bySessionType: sessionTypeStats
  };
};

// 静态方法：获取学习趋势
learningRecordSchema.statics.getLearningTrend = async function(userId, days = 30) {
  const endDate = new Date();
  const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000));
  
  return await this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        startTime: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$startTime' },
          month: { $month: '$startTime' },
          day: { $dayOfMonth: '$startTime' }
        },
        sessions: { $sum: 1 },
        totalDuration: { $sum: '$duration' },
        avgEffectiveness: { $avg: '$effectiveness' },
        avgAccuracy: { $avg: '$performance.accuracy' }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
    }
  ]);
};

// 静态方法：获取最佳学习时间
learningRecordSchema.statics.getBestLearningTimes = async function(userId) {
  return await this.aggregate([
    {
      $match: {
        userId: new mongoose.Types.ObjectId(userId),
        effectiveness: { $gte: 4 } // 只考虑效果好的学习记录
      }
    },
    {
      $group: {
        _id: { $hour: '$startTime' },
        sessions: { $sum: 1 },
        avgEffectiveness: { $avg: '$effectiveness' },
        avgFocusLevel: { $avg: '$metadata.focusLevel' }
      }
    },
    {
      $sort: { avgEffectiveness: -1, sessions: -1 }
    },
    {
      $limit: 5
    }
  ]);
};

// 静态方法：获取学习模式分析
learningRecordSchema.statics.getLearningPatterns = async function(userId) {
  const [timePatterns, subjectPatterns, performancePatterns] = await Promise.all([
    // 时间模式
    this.aggregate([
      {
        $match: { userId: new mongoose.Types.ObjectId(userId) }
      },
      {
        $group: {
          _id: {
            hour: { $hour: '$startTime' },
            dayOfWeek: { $dayOfWeek: '$startTime' }
          },
          sessions: { $sum: 1 },
          avgEffectiveness: { $avg: '$effectiveness' }
        }
      }
    ]),
    
    // 学科模式
    this.aggregate([
      {
        $match: { userId: new mongoose.Types.ObjectId(userId) }
      },
      {
        $group: {
          _id: '$metadata.subject',
          sessions: { $sum: 1 },
          totalDuration: { $sum: '$duration' },
          avgEffectiveness: { $avg: '$effectiveness' },
          avgAccuracy: { $avg: '$performance.accuracy' }
        }
      },
      { $sort: { sessions: -1 } }
    ]),
    
    // 表现模式
    this.aggregate([
      {
        $match: { 
          userId: new mongoose.Types.ObjectId(userId),
          'performance.totalQuestions': { $gt: 0 }
        }
      },
      {
        $group: {
          _id: {
            difficulty: '$metadata.difficulty',
            sessionType: '$sessionType'
          },
          sessions: { $sum: 1 },
          avgAccuracy: { $avg: '$performance.accuracy' },
          avgSpeed: { $avg: '$performance.speed' }
        }
      }
    ])
  ]);
  
  return {
    timePatterns,
    subjectPatterns,
    performancePatterns
  };
};

module.exports = mongoose.model('LearningRecord', learningRecordSchema);
