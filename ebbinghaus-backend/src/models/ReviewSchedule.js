// src/models/ReviewSchedule.js
const mongoose = require('mongoose');
const { REVIEW_STATUS, EFFECTIVENESS_SCORES } = require('../utils/constants');

const reviewScheduleSchema = new mongoose.Schema({
  scheduleId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  taskId: {
    type: String,
    ref: 'Task',
    required: true,
    index: true
  },
  userId: {
    type: String,
    ref: 'User',
    required: true,
    index: true
  },
  reviewIndex: {
    type: Number,
    required: true,
    min: 1,
    max: 9
  },
  intervalDays: {
    type: Number,
    required: true
  },
  scheduledTime: {
    type: Date,
    required: true,
    index: true
  },
  actualTime: Date,
  status: {
    type: String,
    enum: Object.values(REVIEW_STATUS),
    default: REVIEW_STATUS.SCHEDULED,
    index: true
  },
  effectiveness: {
    type: Number,
    min: EFFECTIVENESS_SCORES.VERY_POOR,
    max: EFFECTIVENESS_SCORES.EXCELLENT
  },
  duration: {
    type: Number, // 实际复习时长（分钟）
    min: 0
  },
  notes: {
    type: String,
    maxlength: 500
  },
  adjustedTime: Date // 调整后的时间
}, {
  timestamps: true,
  collection: 'review_schedules'
});

// 复合索引
reviewScheduleSchema.index({ userId: 1, scheduledTime: 1 });
reviewScheduleSchema.index({ taskId: 1, reviewIndex: 1 });
reviewScheduleSchema.index({ userId: 1, status: 1, scheduledTime: 1 });

// 中间件：检查逾期状态
reviewScheduleSchema.pre('find', function() {
  this.updateOverdueStatus();
});

reviewScheduleSchema.pre('findOne', function() {
  this.updateOverdueStatus();
});

// 静态方法：更新逾期状态
reviewScheduleSchema.statics.updateOverdueStatus = function() {
  const now = new Date();
  return this.updateMany(
    {
      status: REVIEW_STATUS.SCHEDULED,
      scheduledTime: { $lt: now }
    },
    {
      $set: { status: REVIEW_STATUS.OVERDUE }
    }
  );
};

// 实例方法：开始复习
reviewScheduleSchema.methods.startReview = function() {
  this.status = REVIEW_STATUS.IN_PROGRESS;
  this.actualTime = new Date();
  return this.save();
};

// 实例方法：完成复习
reviewScheduleSchema.methods.completeReview = function(effectiveness, notes = '') {
  const startTime = this.actualTime || this.scheduledTime;
  const endTime = new Date();

  this.status = REVIEW_STATUS.COMPLETED;
  this.effectiveness = effectiveness;
  this.duration = Math.round((endTime - startTime) / (1000 * 60));
  this.notes = notes;

  return this.save();
};

// 实例方法：跳过复习
reviewScheduleSchema.methods.skipReview = function(reason = '') {
  this.status = REVIEW_STATUS.SKIPPED;
  this.notes = reason;
  return this.save();
};

// 实例方法：调整复习时间
reviewScheduleSchema.methods.adjustScheduleTime = function(newTime) {
  this.adjustedTime = newTime;
  this.scheduledTime = newTime;
  this.status = REVIEW_STATUS.SCHEDULED;
  return this.save();
};

// 静态方法：获取用户的复习计划
reviewScheduleSchema.statics.getUserReviewSchedule = function(userId, options = {}) {
  const query = { userId };
  
  if (options.status) {
    query.status = options.status;
  }
  
  if (options.dateRange) {
    query.scheduledTime = {
      $gte: options.dateRange.start,
      $lte: options.dateRange.end
    };
  }
  
  return this.find(query)
    .populate('taskId', 'title metadata.subject metadata.difficulty')
    .sort({ scheduledTime: 1 });
};

// 静态方法：获取今日复习任务
reviewScheduleSchema.statics.getTodayReviews = function(userId) {
  const today = new Date();
  const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
  const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
  
  return this.find({
    userId,
    scheduledTime: {
      $gte: startOfDay,
      $lt: endOfDay
    },
    status: { $in: [REVIEW_STATUS.SCHEDULED, REVIEW_STATUS.OVERDUE] }
  })
  .populate('taskId', 'title content.text metadata')
  .sort({ scheduledTime: 1 });
};

// 静态方法：获取逾期复习任务
reviewScheduleSchema.statics.getOverdueReviews = function(userId) {
  return this.find({
    userId,
    status: REVIEW_STATUS.OVERDUE
  })
  .populate('taskId', 'title metadata.subject metadata.priority')
  .sort({ scheduledTime: 1 });
};

// 静态方法：获取复习统计
reviewScheduleSchema.statics.getReviewStats = async function(userId, dateRange = null) {
  const matchQuery = { userId: userId };
  
  if (dateRange) {
    matchQuery.scheduledTime = {
      $gte: dateRange.start,
      $lte: dateRange.end
    };
  }
  
  const stats = await this.aggregate([
    { $match: matchQuery },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgEffectiveness: { $avg: '$effectiveness' },
        totalDuration: { $sum: '$duration' }
      }
    }
  ]);
  
  const result = {
    total: 0,
    scheduled: 0,
    completed: 0,
    skipped: 0,
    overdue: 0,
    averageEffectiveness: 0,
    totalDuration: 0,
    completionRate: 0
  };
  
  stats.forEach(stat => {
    result.total += stat.count;
    result.totalDuration += stat.totalDuration || 0;
    
    switch (stat._id) {
      case REVIEW_STATUS.SCHEDULED:
        result.scheduled = stat.count;
        break;
      case REVIEW_STATUS.COMPLETED:
        result.completed = stat.count;
        result.averageEffectiveness = stat.avgEffectiveness || 0;
        break;
      case REVIEW_STATUS.SKIPPED:
        result.skipped = stat.count;
        break;
      case REVIEW_STATUS.OVERDUE:
        result.overdue = stat.count;
        break;
    }
  });
  
  if (result.total > 0) {
    result.completionRate = Math.round((result.completed / result.total) * 100);
  }
  
  return result;
};

// 静态方法：获取复习效果趋势
reviewScheduleSchema.statics.getEffectivenessTrend = async function(userId, days = 30) {
  const endDate = new Date();
  const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000));
  
  return await this.aggregate([
    {
      $match: {
        userId: userId,
        status: REVIEW_STATUS.COMPLETED,
        actualTime: { $gte: startDate, $lte: endDate }
      }
    },
    {
      $group: {
        _id: {
          year: { $year: '$actualTime' },
          month: { $month: '$actualTime' },
          day: { $dayOfMonth: '$actualTime' }
        },
        avgEffectiveness: { $avg: '$effectiveness' },
        reviewCount: { $sum: 1 }
      }
    },
    {
      $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 }
    }
  ]);
};

// 虚拟字段：是否逾期
reviewScheduleSchema.virtual('isOverdue').get(function() {
  return this.status === REVIEW_STATUS.OVERDUE || 
         (this.status === REVIEW_STATUS.SCHEDULED && this.scheduledTime < new Date());
});

// 虚拟字段：距离复习时间
reviewScheduleSchema.virtual('timeUntilReview').get(function() {
  const now = new Date();
  const timeDiff = this.scheduledTime.getTime() - now.getTime();
  return Math.round(timeDiff / (1000 * 60)); // 返回分钟数
});

module.exports = mongoose.model('ReviewSchedule', reviewScheduleSchema);
