{"config": {"filename": "frontend-analysis.json", "baseDirectory": "E:/JYZS/ebbinghaus-learning-system", "projectRoot": "E:/JYZS", "lastUpdated": "2025-08-03T14:28:01.765Z"}, "fileTree": {"path": "E:\\JYZS\\ebbinghaus-learning-system", "name": "ebbinghaus-learning-system", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\.env.development", "name": ".env.development", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\.env.production", "name": ".env.production", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist", "name": "dist", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets", "name": "assets", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ApiTestView-BVxRMBdN.css", "name": "ApiTestView-BVxRMBdN.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ApiTestView-fYkpcgSW.js", "name": "ApiTestView-fYkpcgSW.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ApiTestView-fYkpcgSW.js.map", "name": "ApiTestView-fYkpcgSW.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\AppLayout-BNYRYDsa.js", "name": "AppLayout-BNYRYDsa.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\AppLayout-BNYRYDsa.js.map", "name": "AppLayout-BNYRYDsa.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ComponentTestView-D3DTb0iE.css", "name": "ComponentTestView-D3DTb0iE.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ComponentTestView-D4pzNUcT.js", "name": "ComponentTestView-D4pzNUcT.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ComponentTestView-D4pzNUcT.js.map", "name": "ComponentTestView-D4pzNUcT.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\DashboardView-BNy4ZNFB.js", "name": "DashboardView-BNy4ZNFB.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\DashboardView-BNy4ZNFB.js.map", "name": "DashboardView-BNy4ZNFB.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\DashboardView-DbJudnRo.css", "name": "DashboardView-DbJudnRo.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\HomeView-BucT7Hhy.js", "name": "HomeView-BucT7Hhy.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\HomeView-BucT7Hhy.js.map", "name": "HomeView-BucT7Hhy.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\HomeView-C2oNt8ZK.css", "name": "HomeView-C2oNt8ZK.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-DalhcT85.css", "name": "index-DalhcT85.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-DfjgeC5z.js", "name": "index-DfjgeC5z.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-DfjgeC5z.js.map", "name": "index-DfjgeC5z.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js", "name": "index-Oeuw5Cvn.js", "isDirectory": false, "importance": 5, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\HomeView-BucT7Hhy.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TestView-DWL-s5qg.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ApiTestView-fYkpcgSW.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\StoreTestView-CYNGWrzo.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ComponentTestView-D4pzNUcT.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\DashboardView-BNy4ZNFB.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TasksView-crze5JO2.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskDetailView-C3q_qLqY.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewsView-BnKJe1Gi.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewSessionView-F8ureHw_.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapsView-BPcPzjj9.js", "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapDetailView-DwawTnhb.js"], "packageDependencies": [{"name": "util", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\util"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js.map", "name": "index-Oeuw5Cvn.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapCard-33CezlUi.css", "name": "MindMapCard-33CezlUi.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapCard-CqqMxIGg.js", "name": "MindMapCard-CqqMxIGg.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapCard-CqqMxIGg.js.map", "name": "MindMapCard-CqqMxIGg.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapDetailView-DcwAGp1z.css", "name": "MindMapDetailView-DcwAGp1z.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapDetailView-DwawTnhb.js", "name": "MindMapDetailView-DwawTnhb.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapDetailView-DwawTnhb.js.map", "name": "MindMapDetailView-DwawTnhb.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapsView-BPcPzjj9.js", "name": "MindMapsView-BPcPzjj9.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapsView-BPcPzjj9.js.map", "name": "MindMapsView-BPcPzjj9.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapsView-NFrqTSu5.css", "name": "MindMapsView-NFrqTSu5.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapViewer-BPFIGvLL.js", "name": "MindMapViewer-BPFIGvLL.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\MindMapViewer-BPFIGvLL.js.map", "name": "MindMapViewer-BPFIGvLL.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewCard-Df-3j-Bk.js", "name": "ReviewCard-Df-3j-Bk.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewCard-Df-3j-Bk.js.map", "name": "ReviewCard-Df-3j-Bk.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewSessionView-DU6ceV1b.css", "name": "ReviewSessionView-DU6ceV1b.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewSessionView-F8ureHw_.js", "name": "ReviewSessionView-F8ureHw_.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewSessionView-F8ureHw_.js.map", "name": "ReviewSessionView-F8ureHw_.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewsView-BnKJe1Gi.js", "name": "ReviewsView-BnKJe1Gi.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewsView-BnKJe1Gi.js.map", "name": "ReviewsView-BnKJe1Gi.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\ReviewsView-DV4Z4n6z.css", "name": "ReviewsView-DV4Z4n6z.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\StoreTestView-C3U6VcES.css", "name": "StoreTestView-C3U6VcES.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\StoreTestView-CYNGWrzo.js", "name": "StoreTestView-CYNGWrzo.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\StoreTestView-CYNGWrzo.js.map", "name": "StoreTestView-CYNGWrzo.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskDetailView-C3q_qLqY.js", "name": "TaskDetailView-C3q_qLqY.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskDetailView-C3q_qLqY.js.map", "name": "TaskDetailView-C3q_qLqY.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskDetailView-CuCs1Yr0.css", "name": "TaskDetailView-CuCs1Yr0.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskList-Vc7eSG33.js", "name": "TaskList-Vc7eSG33.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TaskList-Vc7eSG33.js.map", "name": "TaskList-Vc7eSG33.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TasksView-CCygG3aS.css", "name": "TasksView-CCygG3aS.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TasksView-crze5JO2.js", "name": "TasksView-crze5JO2.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TasksView-crze5JO2.js.map", "name": "TasksView-crze5JO2.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TestView-DWL-s5qg.js", "name": "TestView-DWL-s5qg.js", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\index-Oeuw5Cvn.js"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TestView-DWL-s5qg.js.map", "name": "TestView-DWL-s5qg.js.map", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\assets\\TestView-MHhXiZch.css", "name": "TestView-MHhXiZch.css", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\dist\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\docs", "name": "docs", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\env.d.ts", "name": "env.d.ts", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "vue", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\vue", "version": "^3.4.0"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\FileScopeMCP-tree-ebbinghaus-learning-system.json", "name": "FileScopeMCP-tree-ebbinghaus-learning-system.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\package-lock.json", "name": "package-lock.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\package.json", "name": "package.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src", "name": "src", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\App.vue", "name": "App.vue", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\main.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components", "name": "components", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business", "name": "business", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapCard.vue", "name": "MindMapCard.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapEdge.vue", "name": "MindMapEdge.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapNode.vue", "name": "MindMapNode.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapViewer.vue", "name": "MindMapViewer.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\ReviewCard.vue", "name": "ReviewCard.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\TaskCard.vue", "name": "TaskCard.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\TaskList.vue", "name": "TaskList.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\common", "name": "common", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout", "name": "layout", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppHeader.vue", "name": "AppHeader.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppLayout.vue", "name": "AppLayout.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppNotifications.vue", "name": "AppNotifications.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppSidebar.vue", "name": "AppSidebar.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\components\\mindmap", "name": "mindmap", "isDirectory": true, "children": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\constants", "name": "constants", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\constants\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\main.ts", "name": "main.ts", "isDirectory": false, "importance": 10, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\App.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\router", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores", "E:\\JYZS\\ebbinghaus-learning-system\\src\\styles\\index.css"], "packageDependencies": [{"name": "vue", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\vue", "version": "^3.4.0"}, {"name": "pinia", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\pinia", "version": "^2.1.7"}, {"name": "element-plus", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\element-plus", "version": "^2.4.4"}, {"name": "element-plus", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\element-plus\\dist\\index.css", "version": "^2.4.4"}, {"name": "@element-plus/icons-vue", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@element-plus\\icons-vue", "scope": "@element-plus", "version": "^2.3.1"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\router", "name": "router", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 10, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\HomeView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TestView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ApiTestView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\StoreTestView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ComponentTestView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\DashboardView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TasksView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TaskDetailView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewsView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewSessionView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapsView.vue", "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapDetailView.vue"], "packageDependencies": [{"name": "vue-router", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\vue-router", "version": "^4.2.5"}], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services", "name": "services", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api", "name": "api", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\analytics.ts", "name": "analytics.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\task.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\review.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\mindmap.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\user.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\notification.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\analytics.ts"], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\mindmap.ts", "name": "mindmap.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\notification.ts", "name": "notification.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\review.ts", "name": "review.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\task.ts", "name": "task.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\user.ts", "name": "user.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}]}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores", "name": "stores", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts", "name": "app.ts", "isDirectory": false, "importance": 10, "dependencies": [], "packageDependencies": [{"name": "pinia", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\pinia", "version": "^2.1.7"}, {"name": "vue", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\vue", "version": "^3.4.0"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\mindmap.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\review.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\task.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\user.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 9, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\user.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\task.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\review.ts", "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\mindmap.ts"], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\mindmap.ts", "name": "mindmap.ts", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\pinia", "version": "^2.1.7"}, {"name": "vue", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\vue", "version": "^3.4.0"}, {"name": "@/services", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\review.ts", "name": "review.ts", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\pinia", "version": "^2.1.7"}, {"name": "vue", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\vue", "version": "^3.4.0"}, {"name": "@/services", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\task.ts", "name": "task.ts", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\pinia", "version": "^2.1.7"}, {"name": "vue", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\vue", "version": "^3.4.0"}, {"name": "@/services", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\user.ts", "name": "user.ts", "isDirectory": false, "importance": 8, "dependencies": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\pinia", "version": "^2.1.7"}, {"name": "vue", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\vue", "version": "^3.4.0"}, {"name": "@/services", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\styles", "name": "styles", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\styles\\index.css", "name": "index.css", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\main.ts"]}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\types", "name": "types", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\types\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\utils", "name": "utils", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\utils\\http.ts", "name": "http.ts", "isDirectory": false, "importance": 6, "dependencies": [], "packageDependencies": [{"name": "axios", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\axios", "version": "^1.6.2"}, {"name": "element-plus", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\element-plus", "version": "^2.4.4"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\utils\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 8, "dependencies": [], "packageDependencies": [{"name": "dayjs", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\dayjs", "version": "^1.11.10"}, {"name": "dayjs", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\dayjs\\locale\\zh-cn", "version": "^1.11.10"}, {"name": "dayjs", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\dayjs\\plugin\\relativeTime", "version": "^1.11.10"}, {"name": "dayjs", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\dayjs\\plugin\\duration", "version": "^1.11.10"}, {"name": "@/types", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@\\types", "scope": "@"}], "dependents": []}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views", "name": "views", "isDirectory": true, "children": [{"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\analytics", "name": "analytics", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ApiTestView.vue", "name": "ApiTestView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ComponentTestView.vue", "name": "ComponentTestView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\dashboard", "name": "dashboard", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\DashboardView.vue", "name": "DashboardView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\HomeView.vue", "name": "HomeView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\mindmap", "name": "mindmap", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapDetailView.vue", "name": "MindMapDetailView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapsView.vue", "name": "MindMapsView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\review", "name": "review", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewSessionView.vue", "name": "ReviewSessionView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewsView.vue", "name": "ReviewsView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\StoreTestView.vue", "name": "StoreTestView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\task", "name": "task", "isDirectory": true, "children": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TaskDetailView.vue", "name": "TaskDetailView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TasksView.vue", "name": "TasksView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TestView.vue", "name": "TestView.vue", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": ["E:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}]}]}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\tsconfig.app.json", "name": "tsconfig.app.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\tsconfig.json", "name": "tsconfig.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\tsconfig.node.json", "name": "tsconfig.node.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\tsconfig.vitest.json", "name": "tsconfig.vitest.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "E:\\JYZS\\ebbinghaus-learning-system\\vite.config.ts", "name": "vite.config.ts", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "vite", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\vite", "version": "^5.0.10", "isDevDependency": true}, {"name": "@vitejs/plugin-vue", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\@vitejs\\plugin-vue", "scope": "@vitejs", "version": "^4.5.2", "isDevDependency": true}, {"name": "node:url", "path": "E:\\JYZS\\ebbinghaus-learning-system\\node_modules\\node:url"}], "dependents": []}]}}