graph TB
classDef package-node fill:#a29bfe,stroke:#2d3436,shape:ellipse
classDef package-scope-node fill:#ffeaa7,stroke:#2d3436,shape:stadium

  %% Package Scopes

  %% Node Definitions & Styles
  node0["ebbinghaus-learning-system"];
  style node0 fill:#74b9ff,stroke:#333,stroke-width:1px
  node1["dist"];
  style node1 fill:#74b9ff,stroke:#333,stroke-width:1px
  node2["docs"];
  style node2 fill:#74b9ff,stroke:#333,stroke-width:1px
  node3["env.d.ts"];
  style node3 fill:#81ecec,stroke:#333,stroke-width:1px
  node4["package.json"];
  style node4 fill:#81ecec,stroke:#333,stroke-width:1px
  node5["src"];
  style node5 fill:#74b9ff,stroke:#333,stroke-width:1px
  node6["tsconfig.json"];
  style node6 fill:#81ecec,stroke:#333,stroke-width:1px
  node7["vite.config.ts"];
  style node7 fill:#81ecec,stroke:#333,stroke-width:1px
  node8["assets"];
  style node8 fill:#74b9ff,stroke:#333,stroke-width:1px
  node9["vue v\^3.4.0"];
  style node9 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node9 package-node
  node10["App.vue"];
  style node10 fill:#74b9ff,stroke:#333,stroke-width:1px
  node11["components"];
  style node11 fill:#74b9ff,stroke:#333,stroke-width:1px
  node12["constants"];
  style node12 fill:#74b9ff,stroke:#333,stroke-width:1px
  node13["main.ts"];
  style node13 fill:#ff7675,stroke:#333,stroke-width:1px
  node14["router"];
  style node14 fill:#74b9ff,stroke:#333,stroke-width:1px
  node15["services"];
  style node15 fill:#74b9ff,stroke:#333,stroke-width:1px
  node16["stores"];
  style node16 fill:#74b9ff,stroke:#333,stroke-width:1px
  node17["styles"];
  style node17 fill:#74b9ff,stroke:#333,stroke-width:1px
  node18["types"];
  style node18 fill:#74b9ff,stroke:#333,stroke-width:1px
  node19["utils"];
  style node19 fill:#74b9ff,stroke:#333,stroke-width:1px
  node20["views"];
  style node20 fill:#74b9ff,stroke:#333,stroke-width:1px
  node21["pinia v\^2.1.7"];
  style node21 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node21 package-node
  node22["element-plus v\^2.4.4"];
  style node22 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node22 package-node
  node23["element-plus v\^2.4.4"];
  style node23 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node23 package-node
  node24["_element-plus_icons-vue v\^..."];
  style node24 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node24 package-node
  node27["vite v\^5.0.10 \[dev\]"];
  style node27 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node27 package-node
  node28["_vitejs_plugin-vue v\^4.5.2..."];
  style node28 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node28 package-node
  node31["node_url"];
  style node31 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node31 package-node

  %% Edge Definitions
  node0 --> node1
  linkStyle 0 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node2
  linkStyle 1 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node3
  linkStyle 2 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node4
  linkStyle 3 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node5
  linkStyle 4 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node6
  linkStyle 5 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node7
  linkStyle 6 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node8
  linkStyle 7 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node9
  linkStyle 8 stroke:#6c5ce7,stroke-width:1.5px
  node5 --> node10
  linkStyle 9 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node11
  linkStyle 10 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node12
  linkStyle 11 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node13
  linkStyle 12 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node14
  linkStyle 13 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node15
  linkStyle 14 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node16
  linkStyle 15 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node17
  linkStyle 16 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node18
  linkStyle 17 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node19
  linkStyle 18 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node20
  linkStyle 19 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node13 --> node10
  linkStyle 20 stroke:#636e72,stroke-width:1px
  node13 --> node14
  linkStyle 21 stroke:#636e72,stroke-width:1px
  node13 --> node16
  linkStyle 22 stroke:#636e72,stroke-width:1px
  node13 --> node9
  linkStyle 23 stroke:#6c5ce7,stroke-width:1.5px
  node13 --> node21
  linkStyle 24 stroke:#6c5ce7,stroke-width:1.5px
  node13 --> node22
  linkStyle 25 stroke:#6c5ce7,stroke-width:1.5px
  node13 --> node23
  linkStyle 26 stroke:#6c5ce7,stroke-width:1.5px
  node13 --> node24
  linkStyle 27 stroke:#6c5ce7,stroke-width:1.5px
  node7 --> node27
  linkStyle 28 stroke:#6c5ce7,stroke-width:1.5px
  node7 --> node28
  linkStyle 29 stroke:#6c5ce7,stroke-width:1.5px
  node7 --> node31
  linkStyle 30 stroke:#6c5ce7,stroke-width:1.5px