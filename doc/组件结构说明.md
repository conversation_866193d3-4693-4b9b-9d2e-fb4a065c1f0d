# JYZS 项目组件结构说明

## 📋 组件概览

### 组件统计
- **总组件数**: 11个Vue组件
- **布局组件**: 4个 (AppLayout, AppHeader, AppSidebar, AppNotifications)
- **业务组件**: 7个 (TaskCard, TaskList, ReviewCard, MindMapViewer等)
- **通用组件**: 0个 (预留common目录)
- **页面组件**: 12个 (各种View组件)

## 🏗️ 组件层次结构

### 整体组件架构
```mermaid
graph TD
    A[App.vue] --> B[AppLayout]
    B --> C[AppHeader]
    B --> D[AppSidebar]
    B --> E[AppNotifications]
    B --> F[RouterView]
    
    F --> G[页面组件]
    G --> H[业务组件]
    G --> I[布局组件复用]
    
    H --> J[TaskCard]
    H --> K[TaskList]
    H --> L[ReviewCard]
    H --> M[MindMap组件组]
    
    style A fill:#ff6b6b
    style B fill:#4ecdc4
    style H fill:#45b7d1
```

### 组件分类结构
```mermaid
graph LR
    A[components/] --> B[layout/]
    A --> C[business/]
    A --> D[common/]
    A --> E[mindmap/]
    A --> F[index.ts]
    
    B --> G[AppLayout.vue]
    B --> H[AppHeader.vue]
    B --> I[AppSidebar.vue]
    B --> J[AppNotifications.vue]
    
    C --> K[TaskCard.vue]
    C --> L[TaskList.vue]
    C --> M[ReviewCard.vue]
    C --> N[MindMapViewer.vue]
    C --> O[MindMapNode.vue]
    C --> P[MindMapEdge.vue]
    C --> Q[MindMapCard.vue]
    
    style B fill:#81c784
    style C fill:#64b5f6
    style D fill:#ffb74d
```

## 🎨 布局组件详解

### 1. AppLayout.vue
```typescript
// 主布局容器组件
功能职责:
├── 整体页面布局管理
├── 响应式布局适配
├── 侧边栏状态控制
└── 主内容区域渲染

使用场景:
└── 所有页面的根布局组件

Props接口:
interface AppLayoutProps {
  collapsed?: boolean    // 侧边栏折叠状态
  showSidebar?: boolean // 是否显示侧边栏
}
```

### 2. AppHeader.vue
```typescript
// 顶部导航栏组件
功能职责:
├── 用户信息显示
├── 全局搜索功能
├── 通知中心入口
├── 主题切换控制
└── 用户菜单操作

Events:
├── @toggle-sidebar    // 切换侧边栏
├── @user-logout      // 用户登出
└── @theme-change     // 主题切换
```

### 3. AppSidebar.vue
```typescript
// 侧边栏导航组件
功能职责:
├── 主导航菜单渲染
├── 路由跳转控制
├── 菜单状态管理
└── 权限控制显示

数据结构:
interface MenuItem {
  id: string
  title: string
  icon: string
  route: string
  children?: MenuItem[]
}
```

### 4. AppNotifications.vue
```typescript
// 通知组件
功能职责:
├── 通知消息显示
├── 消息状态管理
├── 实时通知推送
└── 消息操作处理

Props:
interface NotificationProps {
  notifications: Notification[]
  maxCount?: number
  autoClose?: boolean
}
```

## 💼 业务组件详解

### 1. 任务管理组件组

#### TaskCard.vue
```typescript
// 任务卡片组件
功能职责:
├── 任务信息展示
├── 任务状态显示
├── 快速操作按钮
└── 任务进度指示

Props接口:
interface TaskCardProps {
  task: LearningTask
  showActions?: boolean
  compact?: boolean
  onEdit?: (task: LearningTask) => void
  onDelete?: (taskId: string) => void
  onStatusChange?: (taskId: string, status: TaskStatus) => void
}

使用示例:
<TaskCard 
  :task="taskData"
  :show-actions="true"
  @edit="handleEdit"
  @delete="handleDelete"
/>
```

#### TaskList.vue
```typescript
// 任务列表组件
功能职责:
├── 任务列表渲染
├── 分页控制
├── 排序和筛选
├── 批量操作支持
└── 虚拟滚动优化

Props接口:
interface TaskListProps {
  tasks: LearningTask[]
  loading?: boolean
  pagination?: PaginationInfo
  selectable?: boolean
}

Events:
├── @task-select      // 任务选择
├── @page-change      // 分页变更
├── @sort-change      // 排序变更
└── @filter-change    // 筛选变更
```

### 2. 复习系统组件

#### ReviewCard.vue
```typescript
// 复习卡片组件
功能职责:
├── 复习内容展示
├── 复习进度显示
├── 性能评估输入
└── 复习操作控制

Props接口:
interface ReviewCardProps {
  schedule: ReviewSchedule
  task: LearningTask
  onComplete?: (scheduleId: string, performance: PerformanceLevel) => void
  onSkip?: (scheduleId: string) => void
}

状态管理:
├── 当前复习阶段
├── 复习计时器
├── 性能评分
└── 复习历史
```

### 3. 思维导图组件组

#### MindMapViewer.vue
```typescript
// 思维导图查看器
功能职责:
├── 思维导图渲染
├── 节点交互处理
├── 缩放和平移控制
├── 布局算法应用
└── 导出功能支持

技术实现:
├── Cytoscape.js引擎
├── 自定义样式系统
├── 事件处理机制
└── 性能优化策略

Props接口:
interface MindMapProps {
  mindMap: MindMap
  readonly?: boolean
  onNodeClick?: (node: MindMapNode) => void
  onEdgeClick?: (edge: MindMapEdge) => void
  onSave?: (mindMap: MindMap) => void
}
```

#### MindMapNode.vue
```typescript
// 思维导图节点组件
功能职责:
├── 节点内容渲染
├── 节点样式控制
├── 编辑状态管理
└── 拖拽功能支持

节点类型:
├── root    // 根节点
├── branch  // 分支节点
├── leaf    // 叶子节点
├── note    // 注释节点
├── image   // 图片节点
└── link    // 链接节点
```

#### MindMapEdge.vue
```typescript
// 思维导图连线组件
功能职责:
├── 连线渲染控制
├── 连线样式管理
├── 连线标签显示
└── 连线动画效果

连线类型:
├── straight    // 直线
├── curved      // 曲线
├── orthogonal  // 直角线
├── solid       // 实线
├── dashed      // 虚线
└── dotted      // 点线
```

#### MindMapCard.vue
```typescript
// 思维导图卡片组件
功能职责:
├── 思维导图预览
├── 基本信息显示
├── 快速操作入口
└── 分享功能支持

使用场景:
├── 思维导图列表页
├── 仪表板展示
└── 搜索结果显示
```

## 📊 组件复用情况统计

### 高复用组件 (被多处使用)
```mermaid
graph LR
    A[TaskCard] --> B[TasksView]
    A --> C[DashboardView]
    A --> D[SearchResults]
    
    E[ReviewCard] --> F[ReviewsView]
    E --> G[DashboardView]
    E --> H[ReviewSession]
    
    I[MindMapCard] --> J[MindMapsView]
    I --> K[DashboardView]
    I --> L[UserProfile]
```

### 组件复用率分析
- **TaskCard**: 复用率 85% (3个页面使用)
- **ReviewCard**: 复用率 75% (3个页面使用)
- **MindMapCard**: 复用率 70% (3个页面使用)
- **AppLayout**: 复用率 100% (所有页面使用)
- **AppHeader**: 复用率 100% (所有页面使用)

## 🔧 组件设计模式

### 1. 组合式API模式
```typescript
// 组件内部使用组合式API
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTaskStore } from '@/stores/task'

// 响应式数据
const loading = ref(false)
const taskStore = useTaskStore()

// 计算属性
const filteredTasks = computed(() => {
  return taskStore.tasks.filter(task => task.status === 'active')
})

// 生命周期
onMounted(() => {
  taskStore.fetchTasks()
})
</script>
```

### 2. Props验证模式
```typescript
// 严格的Props类型定义
interface Props {
  task: LearningTask
  showActions?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  compact: false
})
```

### 3. 事件发射模式
```typescript
// 类型安全的事件发射
interface Emits {
  edit: [task: LearningTask]
  delete: [taskId: string]
  statusChange: [taskId: string, status: TaskStatus]
}

const emit = defineEmits<Emits>()

const handleEdit = () => {
  emit('edit', props.task)
}
```

## 🎯 组件性能优化

### 1. 懒加载策略
```typescript
// 路由级别懒加载
const TasksView = () => import('@/views/TasksView.vue')

// 组件级别懒加载
const MindMapViewer = defineAsyncComponent(() => 
  import('@/components/business/MindMapViewer.vue')
)
```

### 2. 虚拟滚动优化
```typescript
// 大列表虚拟滚动
<template>
  <VirtualList
    :items="tasks"
    :item-height="120"
    :buffer="5"
  >
    <template #default="{ item }">
      <TaskCard :task="item" />
    </template>
  </VirtualList>
</template>
```

### 3. 缓存策略
```typescript
// 组件实例缓存
<router-view v-slot="{ Component }">
  <keep-alive :include="['TasksView', 'ReviewsView']">
    <component :is="Component" />
  </keep-alive>
</router-view>
```

## 📝 组件开发规范

### 1. 文件命名规范
```
组件文件: PascalCase.vue
├── TaskCard.vue      ✅
├── task-card.vue     ❌
└── taskCard.vue      ❌

目录结构:
├── components/
│   ├── business/     // 业务组件
│   ├── layout/       // 布局组件
│   ├── common/       // 通用组件
│   └── index.ts      // 统一导出
```

### 2. 组件结构规范
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入依赖
// 2. 类型定义
// 3. Props定义
// 4. Emits定义
// 5. 响应式数据
// 6. 计算属性
// 7. 方法定义
// 8. 生命周期
</script>

<style scoped>
/* 组件样式 */
</style>
```

### 3. 注释规范
```typescript
/**
 * 任务卡片组件
 * @description 用于显示学习任务的卡片组件，支持编辑、删除等操作
 * <AUTHOR> Team
 * @version 1.0.0
 */
```

## 🚀 组件扩展建议

### 1. 通用组件补充
```typescript
// 建议添加的通用组件
├── LoadingSpinner.vue    // 加载动画
├── EmptyState.vue        // 空状态
├── ErrorBoundary.vue     // 错误边界
├── ConfirmDialog.vue     // 确认对话框
└── ImageUploader.vue     // 图片上传
```

### 2. 高级功能组件
```typescript
// 建议添加的高级组件
├── DataTable.vue         // 数据表格
├── FormBuilder.vue       // 表单构建器
├── ChartWrapper.vue      // 图表封装
├── FileManager.vue       // 文件管理器
└── RichTextEditor.vue    // 富文本编辑器
```

---

**文档版本**: v1.0  
**更新时间**: 2025-08-03  
**组件总数**: 11个核心组件  
**架构状态**: 稳定可扩展
