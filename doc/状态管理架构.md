# JYZS 项目状态管理架构

## 📋 Pinia状态管理概览

### 架构特点
- **状态管理库**: Pinia 2.1.7
- **设计模式**: 组合式API风格
- **模块数量**: 5个核心Store模块
- **类型安全**: 完整TypeScript支持
- **开发工具**: Vue DevTools集成

### Store模块结构
```
stores/
├── index.ts          # 统一导出和管理
├── app.ts           # 应用基础状态 (重要性: 10/10)
├── user.ts          # 用户状态管理 (重要性: 8/10)
├── task.ts          # 任务状态管理 (重要性: 8/10)
├── review.ts        # 复习状态管理 (重要性: 8/10)
└── mindmap.ts       # 思维导图状态 (重要性: 8/10)
```

## 🏗️ Store架构设计

### 整体状态管理架构
```mermaid
graph TD
    A[stores/index.ts] --> B[统一导出管理]
    A --> C[初始化控制]
    A --> D[重置功能]
    
    E[app.ts] --> F[基础应用状态]
    E --> G[全局配置]
    E --> H[主题管理]
    
    I[user.ts] --> E
    I --> J[用户认证]
    I --> K[用户偏好]
    
    L[task.ts] --> E
    L --> M[任务管理]
    L --> N[任务状态]
    
    O[review.ts] --> E
    O --> P[复习调度]
    O --> Q[复习记录]
    
    R[mindmap.ts] --> E
    R --> S[思维导图]
    R --> T[图形数据]
    
    style E fill:#4caf50
    style A fill:#2196f3
```

### Store依赖关系
```mermaid
graph LR
    A[app.ts] --> B[基础状态]
    
    C[user.ts] --> A
    C --> D[API: user.ts]
    
    E[task.ts] --> A
    E --> F[API: task.ts]
    
    G[review.ts] --> A
    G --> H[API: review.ts]
    
    I[mindmap.ts] --> A
    I --> J[API: mindmap.ts]
    
    style A fill:#ff9800
    style C fill:#4caf50
    style E fill:#2196f3
    style G fill:#9c27b0
    style I fill:#f44336
```

## 🎯 核心Store模块详解

### 1. App Store (app.ts)
```typescript
// 应用基础状态管理
interface AppState {
  // 应用基础信息
  appName: string
  version: string
  
  // UI状态
  loading: boolean
  sidebarCollapsed: boolean
  theme: 'light' | 'dark'
  
  // 全局配置
  config: AppConfig
  
  // 错误处理
  error: string | null
  
  // 通知系统
  notifications: Notification[]
}

// 主要Actions
const actions = {
  initialize,           // 初始化应用
  setLoading,          // 设置加载状态
  toggleSidebar,       // 切换侧边栏
  setTheme,            // 设置主题
  showNotification,    // 显示通知
  clearError,          // 清除错误
  reset                // 重置状态
}

// 主要Getters
const getters = {
  isLoading,           // 是否加载中
  currentTheme,        // 当前主题
  unreadNotifications, // 未读通知数
  hasError            // 是否有错误
}
```

### 2. User Store (user.ts)
```typescript
// 用户状态管理
interface UserState {
  // 用户信息
  currentUser: User | null
  isAuthenticated: boolean
  
  // 用户偏好
  preferences: UserPreferences
  
  // 认证状态
  token: string | null
  refreshToken: string | null
  
  // 加载状态
  loading: boolean
  error: string | null
}

// 核心Actions
const actions = {
  // 认证相关
  login,               // 用户登录
  logout,              // 用户登出
  refreshAuth,         // 刷新认证
  
  // 用户信息
  fetchProfile,        // 获取用户资料
  updateProfile,       // 更新用户资料
  updatePreferences,   // 更新用户偏好
  
  // 状态管理
  initialize,          // 初始化用户状态
  reset               // 重置用户状态
}
```

### 3. Task Store (task.ts)
```typescript
// 任务状态管理
interface TaskState {
  // 任务数据
  tasks: LearningTask[]
  currentTask: LearningTask | null
  
  // 分页信息
  pagination: PaginationInfo | null
  
  // 筛选和排序
  filters: TaskFilters
  sortBy: string
  sortOrder: 'asc' | 'desc'
  
  // 状态控制
  loading: boolean
  error: string | null
}

// 核心Actions
const actions = {
  // 任务CRUD
  fetchTasks,          // 获取任务列表
  createTask,          // 创建任务
  updateTask,          // 更新任务
  deleteTask,          // 删除任务
  
  // 任务操作
  completeTask,        // 完成任务
  pauseTask,           // 暂停任务
  resumeTask,          // 恢复任务
  
  // 筛选排序
  setFilters,          // 设置筛选条件
  setSorting,          // 设置排序
  
  // 状态管理
  setCurrentTask,      // 设置当前任务
  clearError,          // 清除错误
  reset               // 重置状态
}

// 重要Getters
const getters = {
  activeTasks,         // 活跃任务
  completedTasks,      // 已完成任务
  overdueTasksCount,   // 逾期任务数
  tasksByCategory,     // 按分类分组
  taskProgress        // 任务进度统计
}
```

### 4. Review Store (review.ts)
```typescript
// 复习状态管理
interface ReviewState {
  // 复习数据
  schedules: ReviewSchedule[]
  currentSession: ReviewSession | null
  
  // 今日复习
  todayReviews: ReviewSchedule[]
  pendingReviews: ReviewSchedule[]
  
  // 复习统计
  reviewStats: ReviewStats
  
  // 状态控制
  loading: boolean
  error: string | null
}

// 核心Actions
const actions = {
  // 复习调度
  fetchReviewSchedules,    // 获取复习计划
  createReviewSchedule,    // 创建复习计划
  updateReviewSchedule,    // 更新复习计划
  
  // 复习会话
  startReviewSession,      // 开始复习会话
  completeReview,          // 完成复习
  skipReview,              // 跳过复习
  endReviewSession,        // 结束复习会话
  
  // 数据获取
  fetchTodayReviews,       // 获取今日复习
  fetchReviewStats,        // 获取复习统计
  
  // 状态管理
  reset                    // 重置状态
}

// 艾宾浩斯算法Getters
const getters = {
  nextReviewTime,          // 下次复习时间
  reviewEfficiency,        // 复习效率
  retentionRate,           // 记忆保持率
  reviewStreak            // 复习连续天数
}
```

### 5. MindMap Store (mindmap.ts)
```typescript
// 思维导图状态管理
interface MindMapState {
  // 思维导图数据
  mindMaps: MindMap[]
  currentMindMap: MindMap | null
  
  // 编辑状态
  isEditing: boolean
  selectedNodes: string[]
  selectedEdges: string[]
  
  // 视图状态
  viewMode: 'view' | 'edit' | 'present'
  zoomLevel: number
  centerPosition: Position
  
  // 状态控制
  loading: boolean
  error: string | null
}

// 核心Actions
const actions = {
  // 思维导图CRUD
  fetchMindMaps,           // 获取思维导图列表
  createMindMap,           // 创建思维导图
  updateMindMap,           // 更新思维导图
  deleteMindMap,           // 删除思维导图
  
  // 节点操作
  addNode,                 // 添加节点
  updateNode,              // 更新节点
  deleteNode,              // 删除节点
  
  // 连线操作
  addEdge,                 // 添加连线
  updateEdge,              // 更新连线
  deleteEdge,              // 删除连线
  
  // 视图控制
  setViewMode,             // 设置视图模式
  setZoom,                 // 设置缩放
  setCenter,               // 设置中心点
  
  // 选择控制
  selectNodes,             // 选择节点
  selectEdges,             // 选择连线
  clearSelection,          // 清除选择
  
  // 状态管理
  setCurrentMindMap,       // 设置当前思维导图
  toggleEditMode,          // 切换编辑模式
  reset                    // 重置状态
}
```

## 🔄 状态流转机制

### 数据流向图
```mermaid
sequenceDiagram
    participant C as 组件
    participant S as Store
    participant A as API
    participant B as 后端

    C->>S: 调用Action
    S->>S: 设置loading=true
    S->>A: 调用API服务
    A->>B: 发送HTTP请求
    B->>A: 返回响应数据
    A->>S: 返回处理结果
    S->>S: 更新state数据
    S->>S: 设置loading=false
    S->>C: 响应式更新UI
```

### 错误处理流程
```mermaid
graph TD
    A[Action调用] --> B{API请求}
    B -->|成功| C[更新State]
    B -->|失败| D[设置Error]
    D --> E[显示错误通知]
    E --> F[记录错误日志]
    C --> G[触发UI更新]
    F --> H[错误恢复机制]
```

## 📊 状态管理最佳实践

### 1. 状态设计原则
```typescript
// ✅ 正确的状态设计
interface TaskState {
  tasks: LearningTask[]        // 规范化数据
  loading: boolean             // 加载状态
  error: string | null         // 错误信息
  pagination: PaginationInfo   // 分页信息
}

// ❌ 避免的设计
interface BadTaskState {
  tasksWithLoading: {          // 混合数据和状态
    data: LearningTask[]
    loading: boolean
  }
  errorMessage: string         // 非null类型
}
```

### 2. Action设计模式
```typescript
// 标准Action模式
const createTask = async (taskData: CreateTaskForm) => {
  try {
    // 1. 设置加载状态
    loading.value = true
    error.value = null
    
    // 2. 调用API
    const response = await taskAPI.create(taskData)
    
    // 3. 更新状态
    tasks.value.push(response.data)
    
    // 4. 通知成功
    const appStore = useAppStore()
    appStore.showNotification({
      type: 'success',
      message: '任务创建成功'
    })
    
    return response.data
  } catch (err) {
    // 5. 错误处理
    error.value = err.message
    throw err
  } finally {
    // 6. 清理状态
    loading.value = false
  }
}
```

### 3. Getter设计模式
```typescript
// 计算属性Getter
const activeTasks = computed(() => {
  return tasks.value.filter(task => 
    task.status === 'pending' || task.status === 'in_progress'
  )
})

// 参数化Getter
const getTasksByCategory = computed(() => {
  return (categoryId: string) => {
    return tasks.value.filter(task => task.categoryId === categoryId)
  }
})
```

## 🚀 性能优化策略

### 1. 状态分割
```typescript
// 按功能模块分割状态
const useTaskStore = defineStore('task', () => {
  // 只管理任务相关状态
  const tasks = ref<LearningTask[]>([])
  const currentTask = ref<LearningTask | null>(null)
  
  return { tasks, currentTask }
})
```

### 2. 懒加载策略
```typescript
// 按需加载数据
const fetchTasksIfNeeded = async () => {
  if (tasks.value.length === 0) {
    await fetchTasks()
  }
}
```

### 3. 缓存机制
```typescript
// 数据缓存策略
const cache = new Map<string, any>()

const fetchWithCache = async (key: string, fetcher: () => Promise<any>) => {
  if (cache.has(key)) {
    return cache.get(key)
  }
  
  const data = await fetcher()
  cache.set(key, data)
  return data
}
```

## 🔧 Store集成使用

### 1. 组件中使用Store
```vue
<script setup lang="ts">
import { useTaskStore } from '@/stores/task'
import { useAppStore } from '@/stores/app'

// 获取Store实例
const taskStore = useTaskStore()
const appStore = useAppStore()

// 响应式数据
const { tasks, loading, error } = storeToRefs(taskStore)

// 调用Actions
const handleCreateTask = async (taskData: CreateTaskForm) => {
  try {
    await taskStore.createTask(taskData)
    // 任务创建成功后的处理
  } catch (err) {
    // 错误处理
    console.error('创建任务失败:', err)
  }
}

// 使用Getters
const activeTasks = computed(() => taskStore.activeTasks)
</script>
```

### 2. Store间通信
```typescript
// 跨Store通信示例
const completeTask = async (taskId: string) => {
  // 更新任务状态
  await updateTask(taskId, { status: 'completed' })
  
  // 通知复习系统
  const reviewStore = useReviewStore()
  await reviewStore.createReviewSchedule(taskId)
  
  // 更新应用通知
  const appStore = useAppStore()
  appStore.showNotification({
    type: 'success',
    message: '任务完成，已安排复习计划'
  })
}
```

## 📈 状态管理监控

### 1. 开发工具集成
```typescript
// Pinia DevTools配置
const pinia = createPinia()

if (import.meta.env.DEV) {
  pinia.use(({ store }) => {
    // 添加调试信息
    store.$subscribe((mutation, state) => {
      console.log('Store变更:', mutation.type, state)
    })
  })
}
```

### 2. 状态持久化
```typescript
// 状态持久化插件
import { createPersistedState } from 'pinia-plugin-persistedstate'

pinia.use(createPersistedState({
  storage: localStorage,
  include: ['user', 'app'], // 只持久化特定Store
}))
```

---

**架构版本**: v1.0  
**状态管理**: Pinia 2.1.7  
**模块数量**: 5个核心Store  
**类型安全**: 100% TypeScript覆盖
