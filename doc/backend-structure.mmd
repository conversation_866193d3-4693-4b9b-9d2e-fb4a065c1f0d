graph TB
classDef package-node fill:#a29bfe,stroke:#2d3436,shape:ellipse
classDef package-scope-node fill:#ffeaa7,stroke:#2d3436,shape:stadium

  %% Package Scopes

  %% Node Definitions & Styles
  node0["ebbinghaus-backend"];
  style node0 fill:#74b9ff,stroke:#333,stroke-width:1px
  node1["coverage"];
  style node1 fill:#74b9ff,stroke:#333,stroke-width:1px
  node2["docs"];
  style node2 fill:#74b9ff,stroke:#333,stroke-width:1px
  node3["logs"];
  style node3 fill:#74b9ff,stroke:#333,stroke-width:1px
  node4["scripts"];
  style node4 fill:#74b9ff,stroke:#333,stroke-width:1px
  node5["src"];
  style node5 fill:#74b9ff,stroke:#333,stroke-width:1px
  node6["tests"];
  style node6 fill:#74b9ff,stroke:#333,stroke-width:1px
  node7["lcov-report"];
  style node7 fill:#74b9ff,stroke:#333,stroke-width:1px
  node8["src"];
  style node8 fill:#74b9ff,stroke:#333,stroke-width:1px
  node9["health-check.js"];
  style node9 fill:#74b9ff,stroke:#333,stroke-width:1px
  node10["start-server.js"];
  style node10 fill:#ff7675,stroke:#333,stroke-width:1px
  node11["troubleshoot.js"];
  style node11 fill:#74b9ff,stroke:#333,stroke-width:1px
  node12["app.js"];
  style node12 fill:#ff7675,stroke:#333,stroke-width:1px
  node13["config"];
  style node13 fill:#74b9ff,stroke:#333,stroke-width:1px
  node14["controllers"];
  style node14 fill:#74b9ff,stroke:#333,stroke-width:1px
  node15["middleware"];
  style node15 fill:#74b9ff,stroke:#333,stroke-width:1px
  node16["models"];
  style node16 fill:#74b9ff,stroke:#333,stroke-width:1px
  node17["routes"];
  style node17 fill:#74b9ff,stroke:#333,stroke-width:1px
  node18["server.js"];
  style node18 fill:#ff7675,stroke:#333,stroke-width:1px
  node19["services"];
  style node19 fill:#74b9ff,stroke:#333,stroke-width:1px
  node20["utils"];
  style node20 fill:#74b9ff,stroke:#333,stroke-width:1px
  node21["fixtures"];
  style node21 fill:#74b9ff,stroke:#333,stroke-width:1px
  node22["helpers"];
  style node22 fill:#74b9ff,stroke:#333,stroke-width:1px
  node23["integration"];
  style node23 fill:#74b9ff,stroke:#333,stroke-width:1px
  node24["unit"];
  style node24 fill:#74b9ff,stroke:#333,stroke-width:1px

  %% Edge Definitions
  node0 --> node1
  linkStyle 0 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node2
  linkStyle 1 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node3
  linkStyle 2 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node4
  linkStyle 3 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node5
  linkStyle 4 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node6
  linkStyle 5 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node7
  linkStyle 6 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node8
  linkStyle 7 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node9
  linkStyle 8 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node10
  linkStyle 9 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node11
  linkStyle 10 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node12
  linkStyle 11 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node13
  linkStyle 12 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node14
  linkStyle 13 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node15
  linkStyle 14 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node16
  linkStyle 15 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node17
  linkStyle 16 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node18
  linkStyle 17 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node19
  linkStyle 18 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node20
  linkStyle 19 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node21
  linkStyle 20 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node22
  linkStyle 21 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node23
  linkStyle 22 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node24
  linkStyle 23 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5