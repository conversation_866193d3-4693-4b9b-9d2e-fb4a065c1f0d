# JYZS 项目开发维护指南

## 📋 开发环境配置

### 系统要求
- **Node.js**: >= 18.0.0
- **npm**: >= 9.0.0 或 **pnpm**: >= 8.0.0
- **Git**: >= 2.30.0
- **IDE**: VS Code (推荐) + Vue Language Features (Volar)

### 环境安装步骤
```bash
# 1. 克隆项目
git clone <repository-url>
cd ebbinghaus-learning-system

# 2. 安装依赖
npm install
# 或使用 pnpm (推荐)
pnpm install

# 3. 启动开发服务器
npm run dev
# 或
pnpm dev

# 4. 构建生产版本
npm run build
# 或
pnpm build
```

### VS Code 推荐插件
```json
{
  "recommendations": [
    "Vue.volar",                    // Vue 3 语言支持
    "Vue.vscode-typescript-vue-plugin", // TypeScript Vue 插件
    "bradlc.vscode-tailwindcss",    // Tailwind CSS 支持
    "esbenp.prettier-vscode",       // 代码格式化
    "dbaeumer.vscode-eslint",       // ESLint 检查
    "ms-vscode.vscode-typescript-next", // TypeScript 支持
    "antfu.iconify"                 // 图标预览
  ]
}
```

## 🏗️ 项目结构说明

### 核心目录结构
```
ebbinghaus-learning-system/
├── src/
│   ├── components/          # 组件库
│   │   ├── business/       # 业务组件
│   │   ├── layout/         # 布局组件
│   │   ├── common/         # 通用组件
│   │   └── index.ts        # 组件导出
│   ├── stores/             # Pinia 状态管理
│   ├── views/              # 页面组件
│   ├── services/           # API 服务
│   ├── router/             # 路由配置
│   ├── types/              # TypeScript 类型
│   ├── utils/              # 工具函数
│   ├── constants/          # 常量定义
│   └── styles/             # 样式文件
├── docs/                   # 项目文档
├── public/                 # 静态资源
└── 配置文件
```

### 文件命名规范
```
组件文件:     PascalCase.vue     (TaskCard.vue)
页面文件:     PascalCase.vue     (TasksView.vue)
工具文件:     camelCase.ts       (httpClient.ts)
类型文件:     camelCase.ts       (taskTypes.ts)
常量文件:     UPPER_SNAKE_CASE   (API_ENDPOINTS.ts)
样式文件:     kebab-case.css     (task-card.css)
```

## 🚀 开发流程

### 1. 新功能开发流程
```mermaid
graph TD
    A[需求分析] --> B[创建功能分支]
    B --> C[设计API接口]
    C --> D[定义TypeScript类型]
    D --> E[创建Store模块]
    E --> F[开发组件]
    F --> G[编写测试]
    G --> H[代码审查]
    H --> I[合并主分支]
    I --> J[部署测试]
```

### 2. Git 工作流程
```bash
# 创建功能分支
git checkout -b feature/task-management

# 开发过程中提交
git add .
git commit -m "feat: 添加任务管理功能"

# 推送分支
git push origin feature/task-management

# 创建 Pull Request
# 代码审查通过后合并
```

### 3. 提交信息规范
```bash
# 提交类型
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式化
refactor: 代码重构
test:     测试相关
chore:    构建工具或辅助工具的变动

# 提交格式
<type>(<scope>): <description>

# 示例
feat(task): 添加任务创建功能
fix(review): 修复复习计划计算错误
docs(api): 更新API文档
```

## 🎯 开发最佳实践

### 1. Vue 3 组合式API
```vue
<script setup lang="ts">
// ✅ 推荐的组件结构
import { ref, computed, onMounted } from 'vue'
import { useTaskStore } from '@/stores/task'
import type { LearningTask } from '@/types'

// Props 定义
interface Props {
  taskId: string
  readonly?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  readonly: false
})

// Emits 定义
interface Emits {
  update: [task: LearningTask]
  delete: [taskId: string]
}
const emit = defineEmits<Emits>()

// 响应式数据
const loading = ref(false)
const taskStore = useTaskStore()

// 计算属性
const currentTask = computed(() => 
  taskStore.tasks.find(task => task.id === props.taskId)
)

// 方法定义
const handleUpdate = async (taskData: Partial<LearningTask>) => {
  try {
    loading.value = true
    const updatedTask = await taskStore.updateTask(props.taskId, taskData)
    emit('update', updatedTask)
  } catch (error) {
    console.error('更新任务失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  if (!currentTask.value) {
    taskStore.fetchTask(props.taskId)
  }
})
</script>
```

### 2. TypeScript 类型定义
```typescript
// ✅ 完整的类型定义
interface LearningTask {
  id: string
  title: string
  description: string
  type: TaskType
  status: TaskStatus
  createdAt: string
  updatedAt: string
}

// ✅ 使用联合类型
type TaskType = 'reading' | 'practice' | 'memorization'
type TaskStatus = 'pending' | 'in_progress' | 'completed'

// ✅ 泛型接口
interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
}

// ✅ 工具类型使用
type CreateTaskForm = Omit<LearningTask, 'id' | 'createdAt' | 'updatedAt'>
type UpdateTaskForm = Partial<CreateTaskForm> & { id: string }
```

### 3. Pinia Store 开发
```typescript
// ✅ 标准 Store 结构
export const useTaskStore = defineStore('task', () => {
  // State
  const tasks = ref<LearningTask[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const activeTasks = computed(() => 
    tasks.value.filter(task => task.status !== 'completed')
  )

  // Actions
  const fetchTasks = async () => {
    try {
      loading.value = true
      error.value = null
      const response = await taskAPI.getAll()
      tasks.value = response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const createTask = async (taskData: CreateTaskForm) => {
    try {
      loading.value = true
      const response = await taskAPI.create(taskData)
      tasks.value.push(response.data)
      return response.data
    } catch (err) {
      error.value = err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  // 重置函数
  const reset = () => {
    tasks.value = []
    loading.value = false
    error.value = null
  }

  return {
    // State
    tasks: readonly(tasks),
    loading: readonly(loading),
    error: readonly(error),
    
    // Getters
    activeTasks,
    
    // Actions
    fetchTasks,
    createTask,
    reset
  }
})
```

### 4. API 服务开发
```typescript
// ✅ API 服务结构
import { http } from '@/utils/http'
import type { LearningTask, CreateTaskForm, ApiResponse } from '@/types'

export const taskAPI = {
  // 获取任务列表
  getAll: (params?: TaskQueryParams): Promise<ApiResponse<LearningTask[]>> => {
    return http.get('/tasks', { params })
  },

  // 获取单个任务
  getById: (id: string): Promise<ApiResponse<LearningTask>> => {
    return http.get(`/tasks/${id}`)
  },

  // 创建任务
  create: (data: CreateTaskForm): Promise<ApiResponse<LearningTask>> => {
    return http.post('/tasks', data)
  },

  // 更新任务
  update: (id: string, data: Partial<LearningTask>): Promise<ApiResponse<LearningTask>> => {
    return http.put(`/tasks/${id}`, data)
  },

  // 删除任务
  delete: (id: string): Promise<ApiResponse<void>> => {
    return http.delete(`/tasks/${id}`)
  }
}
```

## 🧪 测试策略

### 1. 单元测试 (Vitest)
```typescript
// tests/components/TaskCard.test.ts
import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import TaskCard from '@/components/business/TaskCard.vue'
import type { LearningTask } from '@/types'

const mockTask: LearningTask = {
  id: '1',
  title: '测试任务',
  description: '这是一个测试任务',
  type: 'reading',
  status: 'pending',
  createdAt: '2025-01-01',
  updatedAt: '2025-01-01'
}

describe('TaskCard', () => {
  it('应该正确渲染任务信息', () => {
    const wrapper = mount(TaskCard, {
      props: { task: mockTask }
    })

    expect(wrapper.text()).toContain('测试任务')
    expect(wrapper.text()).toContain('这是一个测试任务')
  })

  it('应该触发编辑事件', async () => {
    const wrapper = mount(TaskCard, {
      props: { task: mockTask, showActions: true }
    })

    await wrapper.find('[data-test="edit-button"]').trigger('click')
    expect(wrapper.emitted('edit')).toBeTruthy()
  })
})
```

### 2. E2E 测试 (Playwright)
```typescript
// tests/e2e/task-management.spec.ts
import { test, expect } from '@playwright/test'

test.describe('任务管理', () => {
  test('应该能够创建新任务', async ({ page }) => {
    await page.goto('/tasks')
    
    // 点击创建任务按钮
    await page.click('[data-test="create-task-button"]')
    
    // 填写任务信息
    await page.fill('[data-test="task-title"]', '新任务')
    await page.fill('[data-test="task-description"]', '任务描述')
    
    // 提交表单
    await page.click('[data-test="submit-button"]')
    
    // 验证任务创建成功
    await expect(page.locator('[data-test="task-list"]')).toContainText('新任务')
  })
})
```

## 🔧 代码质量控制

### 1. ESLint 配置
```javascript
// .eslintrc.cjs
module.exports = {
  extends: [
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier'
  ],
  rules: {
    // Vue 相关规则
    'vue/multi-word-component-names': 'off',
    'vue/no-unused-vars': 'error',
    
    // TypeScript 相关规则
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    
    // 通用规则
    'no-console': 'warn',
    'no-debugger': 'error'
  }
}
```

### 2. Prettier 配置
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 100,
  "endOfLine": "lf"
}
```

### 3. 代码审查清单
```markdown
## 代码审查清单

### 功能性
- [ ] 功能是否按需求正确实现
- [ ] 边界情况是否处理
- [ ] 错误处理是否完善

### 代码质量
- [ ] 代码是否遵循项目规范
- [ ] 变量和函数命名是否清晰
- [ ] 是否有重复代码

### 性能
- [ ] 是否有性能问题
- [ ] 是否正确使用缓存
- [ ] 组件是否正确优化

### 安全性
- [ ] 输入验证是否充分
- [ ] 是否有XSS风险
- [ ] API调用是否安全

### 测试
- [ ] 是否有足够的测试覆盖
- [ ] 测试用例是否有效
- [ ] 是否通过所有测试
```

## 🚀 部署指南

### 1. 构建配置
```typescript
// vite.config.ts
export default defineConfig({
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus'],
          utils: ['axios', 'dayjs', 'lodash-es']
        }
      }
    }
  }
})
```

### 2. 环境配置
```bash
# .env.development
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=JYZS开发环境

# .env.production
VITE_API_BASE_URL=https://api.jyzs.com
VITE_APP_TITLE=JYZS学习系统
```

### 3. Docker 部署
```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 📈 性能优化

### 1. 代码分割
```typescript
// 路由懒加载
const routes = [
  {
    path: '/tasks',
    component: () => import('@/views/TasksView.vue')
  },
  {
    path: '/reviews',
    component: () => import('@/views/ReviewsView.vue')
  }
]

// 组件懒加载
const MindMapViewer = defineAsyncComponent(() =>
  import('@/components/business/MindMapViewer.vue')
)
```

### 2. 缓存策略
```typescript
// HTTP 缓存
const http = axios.create({
  timeout: 10000,
  headers: {
    'Cache-Control': 'max-age=300'
  }
})

// 组件缓存
<router-view v-slot="{ Component }">
  <keep-alive :include="['TasksView', 'ReviewsView']">
    <component :is="Component" />
  </keep-alive>
</router-view>
```

## 🛠️ 故障排除

### 常见问题解决
```bash
# 1. 依赖安装失败
rm -rf node_modules package-lock.json
npm install

# 2. TypeScript 类型错误
npm run type-check

# 3. 构建失败
npm run build -- --mode development

# 4. 开发服务器启动失败
npx vite --force
```

### 调试技巧
```typescript
// 1. Vue DevTools
// 安装 Vue DevTools 浏览器扩展

// 2. 控制台调试
console.log('调试信息:', data)
debugger // 设置断点

// 3. 网络请求调试
// 使用浏览器开发者工具 Network 面板

// 4. 性能分析
// 使用 Vue DevTools Performance 面板
```

---

**指南版本**: v1.0  
**适用项目**: JYZS 艾宾浩斯学习系统  
**技术栈**: Vue 3 + TypeScript + Pinia  
**更新时间**: 2025-08-03
