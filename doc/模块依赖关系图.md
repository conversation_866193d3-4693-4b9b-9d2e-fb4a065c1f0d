# JYZS 项目模块依赖关系分析

## 📊 依赖关系概览

### 统计信息
- **节点数量**: 28个核心模块
- **依赖边数**: 31个依赖关系
- **最大深度**: 2层依赖
- **循环依赖**: 0个 (架构健康)
- **包依赖**: 9个主要包
- **作用域包**: 2个 (@element-plus, @vitejs)

## 🎯 核心模块依赖图

### 主应用依赖结构
```mermaid
graph TD
    A[main.ts] --> B[App.vue]
    A --> C[router/index.ts]
    A --> D[stores/index.ts]
    A --> E[styles/index.css]
    
    C --> F[views/HomeView.vue]
    C --> G[views/DashboardView.vue]
    C --> H[views/TasksView.vue]
    C --> I[views/ReviewsView.vue]
    C --> J[views/MindMapsView.vue]
    C --> K[测试页面组]
    
    D --> L[stores/app.ts]
    D --> M[stores/user.ts]
    D --> N[stores/task.ts]
    D --> O[stores/review.ts]
    D --> P[stores/mindmap.ts]
    
    style A fill:#ff6b6b
    style D fill:#4ecdc4
    style C fill:#45b7d1
```

### Store模块依赖关系
```mermaid
graph LR
    A[stores/index.ts] --> B[app.ts]
    A --> C[user.ts]
    A --> D[task.ts]
    A --> E[review.ts]
    A --> F[mindmap.ts]
    
    C --> B
    D --> B
    E --> B
    F --> B
    
    C --> G[services/api/user.ts]
    D --> H[services/api/task.ts]
    E --> I[services/api/review.ts]
    F --> J[services/api/mindmap.ts]
    
    style A fill:#ffd93d
    style B fill:#6bcf7f
```

### API服务层依赖
```mermaid
graph TD
    A[services/api/index.ts] --> B[task.ts]
    A --> C[review.ts]
    A --> D[mindmap.ts]
    A --> E[user.ts]
    A --> F[notification.ts]
    A --> G[analytics.ts]
    
    B --> H[utils/http.ts]
    C --> H
    D --> H
    E --> H
    F --> H
    G --> H
    
    H --> I[types/index.ts]
    
    style A fill:#ff8a80
    style H fill:#81c784
    style I fill:#64b5f6
```

## 🔍 详细依赖分析

### 1. 入口文件依赖 (main.ts)
```typescript
// 重要性: 10/10 - 应用入口点
依赖项:
├── App.vue (重要性: 5/10)
├── router/index.ts (重要性: 10/10)
├── stores/index.ts (重要性: 9/10)
└── styles/index.css (重要性: 5/10)

外部包依赖:
├── vue (^3.4.0)
├── pinia (^2.1.7)
├── element-plus (^2.4.4)
└── @element-plus/icons-vue (^2.3.1)
```

### 2. 路由模块依赖 (router/index.ts)
```typescript
// 重要性: 10/10 - 路由核心
依赖的视图组件:
├── HomeView.vue
├── DashboardView.vue
├── TasksView.vue
├── TaskDetailView.vue
├── ReviewsView.vue
├── ReviewSessionView.vue
├── MindMapsView.vue
├── MindMapDetailView.vue
└── 测试页面组 (4个)

外部包依赖:
└── vue-router (^4.2.5)
```

### 3. 状态管理依赖链
```mermaid
graph TD
    A[stores/index.ts] --> B[统一导出管理]
    
    C[app.ts] --> D[应用基础状态]
    C --> E[Pinia + Vue]
    C --> F[types/index.ts]
    
    G[user.ts] --> C
    G --> H[services/api/user.ts]
    
    I[task.ts] --> C
    I --> J[services/api/task.ts]
    
    K[review.ts] --> C
    K --> L[services/api/review.ts]
    
    M[mindmap.ts] --> C
    M --> N[services/api/mindmap.ts]
    
    style C fill:#4caf50
    style A fill:#2196f3
```

## 📈 模块重要性排名

### 核心模块 (重要性 9-10)
1. **main.ts** (10/10) - 应用入口
2. **router/index.ts** (10/10) - 路由核心
3. **stores/app.ts** (10/10) - 基础状态管理

### 重要模块 (重要性 7-8)
4. **stores/index.ts** (9/10) - 状态管理统一入口
5. **services/api/index.ts** (9/10) - API服务统一入口
6. **stores/mindmap.ts** (8/10) - 思维导图状态
7. **stores/review.ts** (8/10) - 复习状态
8. **stores/task.ts** (8/10) - 任务状态
9. **stores/user.ts** (8/10) - 用户状态
10. **utils/index.ts** (8/10) - 工具函数

### 功能模块 (重要性 5-7)
11. **components/index.ts** (7/10) - 组件统一导出
12. **constants/index.ts** (7/10) - 常量定义
13. **types/index.ts** (7/10) - 类型定义
14. **各API服务模块** (7/10) - 具体业务API

## 🔄 数据流向分析

### 请求流向
```mermaid
sequenceDiagram
    participant V as Views
    participant S as Stores
    participant A as API Services
    participant U as Utils
    participant T as Types

    V->>S: 调用Store Action
    S->>A: 调用API服务
    A->>U: 使用HTTP工具
    U->>T: 类型验证
    T->>U: 返回类型安全数据
    U->>A: 返回响应
    A->>S: 更新State
    S->>V: 响应式更新UI
```

### 组件依赖流向
```mermaid
graph LR
    A[Pages/Views] --> B[Business Components]
    A --> C[Layout Components]
    B --> D[Common Components]
    
    A --> E[Stores]
    B --> E
    
    E --> F[API Services]
    F --> G[Utils]
    
    style A fill:#ffeb3b
    style E fill:#4caf50
    style F fill:#2196f3
```

## 🚨 依赖风险分析

### 高风险依赖 (需要重点关注)
1. **main.ts** - 单点故障风险
2. **stores/app.ts** - 被多个模块依赖
3. **utils/http.ts** - HTTP请求核心

### 中等风险依赖
1. **router/index.ts** - 路由配置变更影响
2. **types/index.ts** - 类型定义变更影响
3. **services/api/index.ts** - API服务统一入口

### 低风险依赖
1. 各个业务组件 - 相对独立
2. 样式文件 - 影响范围有限
3. 测试相关文件 - 不影响生产

## 🔧 依赖优化建议

### 1. 减少耦合度
```typescript
// 建议: 使用依赖注入模式
// 当前: 直接导入依赖
import { useTaskStore } from '@/stores/task'

// 优化: 通过组合式函数注入
const useTaskManagement = () => {
  const taskStore = useTaskStore()
  return { taskStore }
}
```

### 2. 模块边界清晰化
```typescript
// 建议: 明确模块接口
export interface TaskModule {
  store: TaskStore
  api: TaskAPI
  types: TaskTypes
}
```

### 3. 循环依赖预防
```mermaid
graph TD
    A[定期检查] --> B[依赖分析工具]
    B --> C[发现潜在循环]
    C --> D[重构模块结构]
    D --> A
```

## 📊 包依赖分析

### 生产依赖 (9个核心包)
```json
{
  "vue": "^3.4.0",           // 核心框架
  "vue-router": "^4.2.5",   // 路由管理
  "pinia": "^2.1.7",        // 状态管理
  "element-plus": "^2.4.4", // UI组件库
  "axios": "^1.6.2",        // HTTP客户端
  "dayjs": "^1.11.10",      // 日期处理
  "cytoscape": "^3.28.1",   // 思维导图引擎
  "lodash-es": "^4.17.21",  // 工具函数
  "vuedraggable": "^4.1.0"  // 拖拽功能
}
```

### 开发依赖 (主要工具)
```json
{
  "vite": "^5.0.10",        // 构建工具
  "typescript": "~5.3.0",   // 类型系统
  "eslint": "^8.49.0",      // 代码检查
  "prettier": "^3.0.3",     // 代码格式化
  "vitest": "^1.0.4",       // 单元测试
  "@playwright/test": "^1.40.0" // E2E测试
}
```

## 🎯 依赖管理最佳实践

### 1. 版本管理
- 使用精确版本号避免意外更新
- 定期更新依赖包
- 监控安全漏洞

### 2. 模块设计
- 单一职责原则
- 最小依赖原则
- 接口隔离原则

### 3. 性能优化
- 按需导入减少包体积
- 代码分割优化加载
- Tree-shaking移除死代码

---

**分析工具**: FileScopeMCP  
**分析时间**: 2025-08-03  
**依赖健康度**: 优秀 (无循环依赖)
