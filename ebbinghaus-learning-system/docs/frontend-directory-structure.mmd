graph TB
classDef package-node fill:#a29bfe,stroke:#2d3436,shape:ellipse
classDef package-scope-node fill:#ffeaa7,stroke:#2d3436,shape:stadium

  %% Package Scopes

  %% Node Definitions & Styles
  node0["ebbinghaus-learning-system"];
  style node0 fill:#74b9ff,stroke:#333,stroke-width:1px
  node1["dist"];
  style node1 fill:#74b9ff,stroke:#333,stroke-width:1px
  node2["docs"];
  style node2 fill:#74b9ff,stroke:#333,stroke-width:1px
  node3["env.d.ts"];
  style node3 fill:#81ecec,stroke:#333,stroke-width:1px
  node4["index.html"];
  style node4 fill:#81ecec,stroke:#333,stroke-width:1px
  node5["package.json"];
  style node5 fill:#81ecec,stroke:#333,stroke-width:1px
  node6["src"];
  style node6 fill:#74b9ff,stroke:#333,stroke-width:1px
  node7["tsconfig.json"];
  style node7 fill:#81ecec,stroke:#333,stroke-width:1px
  node8["vite.config.ts"];
  style node8 fill:#81ecec,stroke:#333,stroke-width:1px
  node9["assets"];
  style node9 fill:#74b9ff,stroke:#333,stroke-width:1px
  node10["index.html"];
  style node10 fill:#81ecec,stroke:#333,stroke-width:1px
  node11["ApiTestView-fYkpcgSW.js"];
  style node11 fill:#81ecec,stroke:#333,stroke-width:1px
  node12["AppLayout-BNYRYDsa.js"];
  style node12 fill:#81ecec,stroke:#333,stroke-width:1px
  node13["ComponentTestView-D4pzNUcT.js"];
  style node13 fill:#81ecec,stroke:#333,stroke-width:1px
  node14["DashboardView-BNy4ZNFB.js"];
  style node14 fill:#81ecec,stroke:#333,stroke-width:1px
  node15["HomeView-BucT7Hhy.js"];
  style node15 fill:#81ecec,stroke:#333,stroke-width:1px
  node16["index-DfjgeC5z.js"];
  style node16 fill:#81ecec,stroke:#333,stroke-width:1px
  node17["index-Oeuw5Cvn.js"];
  style node17 fill:#74b9ff,stroke:#333,stroke-width:1px
  node18["MindMapCard-CqqMxIGg.js"];
  style node18 fill:#81ecec,stroke:#333,stroke-width:1px
  node19["MindMapDetailView-DwawTnhb.js"];
  style node19 fill:#81ecec,stroke:#333,stroke-width:1px
  node20["MindMapsView-BPcPzjj9.js"];
  style node20 fill:#81ecec,stroke:#333,stroke-width:1px
  node21["MindMapViewer-BPFIGvLL.js"];
  style node21 fill:#81ecec,stroke:#333,stroke-width:1px
  node22["ReviewCard-Df-3j-Bk.js"];
  style node22 fill:#81ecec,stroke:#333,stroke-width:1px
  node23["ReviewSessionView-F8ureHw_.js"];
  style node23 fill:#81ecec,stroke:#333,stroke-width:1px
  node24["ReviewsView-BnKJe1Gi.js"];
  style node24 fill:#81ecec,stroke:#333,stroke-width:1px
  node25["StoreTestView-CYNGWrzo.js"];
  style node25 fill:#81ecec,stroke:#333,stroke-width:1px
  node26["TaskDetailView-C3q_qLqY.js"];
  style node26 fill:#81ecec,stroke:#333,stroke-width:1px
  node27["TaskList-Vc7eSG33.js"];
  style node27 fill:#81ecec,stroke:#333,stroke-width:1px
  node28["TasksView-crze5JO2.js"];
  style node28 fill:#81ecec,stroke:#333,stroke-width:1px
  node29["TestView-DWL-s5qg.js"];
  style node29 fill:#81ecec,stroke:#333,stroke-width:1px
  node30["App.vue"];
  style node30 fill:#74b9ff,stroke:#333,stroke-width:1px
  node31["components"];
  style node31 fill:#74b9ff,stroke:#333,stroke-width:1px
  node32["constants"];
  style node32 fill:#74b9ff,stroke:#333,stroke-width:1px
  node33["main.ts"];
  style node33 fill:#ff7675,stroke:#333,stroke-width:1px
  node34["router"];
  style node34 fill:#74b9ff,stroke:#333,stroke-width:1px
  node35["services"];
  style node35 fill:#74b9ff,stroke:#333,stroke-width:1px
  node36["stores"];
  style node36 fill:#74b9ff,stroke:#333,stroke-width:1px
  node37["styles"];
  style node37 fill:#74b9ff,stroke:#333,stroke-width:1px
  node38["types"];
  style node38 fill:#74b9ff,stroke:#333,stroke-width:1px
  node39["utils"];
  style node39 fill:#74b9ff,stroke:#333,stroke-width:1px
  node40["views"];
  style node40 fill:#74b9ff,stroke:#333,stroke-width:1px
  node41["business"];
  style node41 fill:#74b9ff,stroke:#333,stroke-width:1px
  node42["common"];
  style node42 fill:#74b9ff,stroke:#333,stroke-width:1px
  node43["index.ts"];
  style node43 fill:#74b9ff,stroke:#333,stroke-width:1px
  node44["layout"];
  style node44 fill:#74b9ff,stroke:#333,stroke-width:1px
  node45["mindmap"];
  style node45 fill:#74b9ff,stroke:#333,stroke-width:1px
  node46["index.ts"];
  style node46 fill:#74b9ff,stroke:#333,stroke-width:1px
  node47["index.ts"];
  style node47 fill:#ff7675,stroke:#333,stroke-width:1px
  node48["api"];
  style node48 fill:#74b9ff,stroke:#333,stroke-width:1px
  node49["app.ts"];
  style node49 fill:#ff7675,stroke:#333,stroke-width:1px
  node50["index.ts"];
  style node50 fill:#ff7675,stroke:#333,stroke-width:1px
  node51["mindmap.ts"];
  style node51 fill:#ff7675,stroke:#333,stroke-width:1px
  node52["review.ts"];
  style node52 fill:#ff7675,stroke:#333,stroke-width:1px
  node53["task.ts"];
  style node53 fill:#ff7675,stroke:#333,stroke-width:1px
  node54["user.ts"];
  style node54 fill:#ff7675,stroke:#333,stroke-width:1px
  node55["index.css"];
  style node55 fill:#74b9ff,stroke:#333,stroke-width:1px
  node56["index.ts"];
  style node56 fill:#74b9ff,stroke:#333,stroke-width:1px
  node57["http.ts"];
  style node57 fill:#74b9ff,stroke:#333,stroke-width:1px
  node58["index.ts"];
  style node58 fill:#ff7675,stroke:#333,stroke-width:1px
  node59["analytics"];
  style node59 fill:#74b9ff,stroke:#333,stroke-width:1px
  node60["ApiTestView.vue"];
  style node60 fill:#81ecec,stroke:#333,stroke-width:1px
  node61["ComponentTestView.vue"];
  style node61 fill:#81ecec,stroke:#333,stroke-width:1px
  node62["dashboard"];
  style node62 fill:#74b9ff,stroke:#333,stroke-width:1px
  node63["DashboardView.vue"];
  style node63 fill:#81ecec,stroke:#333,stroke-width:1px
  node64["HomeView.vue"];
  style node64 fill:#81ecec,stroke:#333,stroke-width:1px
  node65["mindmap"];
  style node65 fill:#74b9ff,stroke:#333,stroke-width:1px
  node66["MindMapDetailView.vue"];
  style node66 fill:#81ecec,stroke:#333,stroke-width:1px
  node67["MindMapsView.vue"];
  style node67 fill:#81ecec,stroke:#333,stroke-width:1px
  node68["review"];
  style node68 fill:#74b9ff,stroke:#333,stroke-width:1px
  node69["ReviewSessionView.vue"];
  style node69 fill:#81ecec,stroke:#333,stroke-width:1px
  node70["ReviewsView.vue"];
  style node70 fill:#81ecec,stroke:#333,stroke-width:1px
  node71["StoreTestView.vue"];
  style node71 fill:#81ecec,stroke:#333,stroke-width:1px
  node72["task"];
  style node72 fill:#74b9ff,stroke:#333,stroke-width:1px
  node73["TaskDetailView.vue"];
  style node73 fill:#81ecec,stroke:#333,stroke-width:1px
  node74["TasksView.vue"];
  style node74 fill:#81ecec,stroke:#333,stroke-width:1px
  node75["TestView.vue"];
  style node75 fill:#81ecec,stroke:#333,stroke-width:1px

  %% Edge Definitions
  node0 --> node1
  linkStyle 0 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node2
  linkStyle 1 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node3
  linkStyle 2 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node4
  linkStyle 3 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node5
  linkStyle 4 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node6
  linkStyle 5 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node7
  linkStyle 6 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node8
  linkStyle 7 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node9
  linkStyle 8 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node1 --> node10
  linkStyle 9 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node11
  linkStyle 10 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node12
  linkStyle 11 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node13
  linkStyle 12 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node14
  linkStyle 13 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node15
  linkStyle 14 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node16
  linkStyle 15 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node17
  linkStyle 16 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node18
  linkStyle 17 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node19
  linkStyle 18 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node20
  linkStyle 19 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node21
  linkStyle 20 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node22
  linkStyle 21 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node23
  linkStyle 22 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node24
  linkStyle 23 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node25
  linkStyle 24 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node26
  linkStyle 25 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node27
  linkStyle 26 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node28
  linkStyle 27 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node9 --> node29
  linkStyle 28 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node30
  linkStyle 29 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node31
  linkStyle 30 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node32
  linkStyle 31 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node33
  linkStyle 32 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node34
  linkStyle 33 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node35
  linkStyle 34 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node36
  linkStyle 35 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node37
  linkStyle 36 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node38
  linkStyle 37 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node39
  linkStyle 38 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node40
  linkStyle 39 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node31 --> node41
  linkStyle 40 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node31 --> node42
  linkStyle 41 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node31 --> node43
  linkStyle 42 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node31 --> node44
  linkStyle 43 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node31 --> node45
  linkStyle 44 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node32 --> node46
  linkStyle 45 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node34 --> node47
  linkStyle 46 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node35 --> node48
  linkStyle 47 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node36 --> node49
  linkStyle 48 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node36 --> node50
  linkStyle 49 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node36 --> node51
  linkStyle 50 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node36 --> node52
  linkStyle 51 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node36 --> node53
  linkStyle 52 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node36 --> node54
  linkStyle 53 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node37 --> node55
  linkStyle 54 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node38 --> node56
  linkStyle 55 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node39 --> node57
  linkStyle 56 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node39 --> node58
  linkStyle 57 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node59
  linkStyle 58 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node60
  linkStyle 59 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node61
  linkStyle 60 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node62
  linkStyle 61 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node63
  linkStyle 62 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node64
  linkStyle 63 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node65
  linkStyle 64 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node66
  linkStyle 65 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node67
  linkStyle 66 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node68
  linkStyle 67 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node69
  linkStyle 68 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node70
  linkStyle 69 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node71
  linkStyle 70 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node72
  linkStyle 71 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node73
  linkStyle 72 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node74
  linkStyle 73 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node40 --> node75
  linkStyle 74 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5