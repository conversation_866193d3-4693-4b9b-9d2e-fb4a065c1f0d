# JYZS API服务层架构文档

## 📋 API服务概览

JYZS前端项目构建了完整的API服务层架构，提供类型安全、模块化的后端接口调用能力。本文档详细说明API服务的设计理念、模块结构和使用规范。

## 🏗️ 服务层架构设计

### 核心设计原则

1. **模块化分离**: 按业务领域划分API模块
2. **类型安全**: 完整的TypeScript接口定义
3. **统一封装**: 标准化的HTTP客户端
4. **错误处理**: 统一的错误处理机制
5. **拦截器**: 请求/响应拦截和转换

### API模块结构

```
services/
└── api/
    ├── index.ts          # API服务聚合导出 (重要性: 9)
    ├── task.ts          # 任务相关API (重要性: 7)
    ├── review.ts        # 复习相关API (重要性: 7)
    ├── mindmap.ts       # 思维导图API (重要性: 7)
    ├── user.ts          # 用户相关API (重要性: 7)
    ├── notification.ts  # 通知API (重要性: 7)
    └── analytics.ts     # 分析统计API (重要性: 7)
```

## 🔧 HTTP客户端封装

### 基础HTTP工具 (utils/http.ts)

**核心功能**:
- Axios实例配置
- 请求/响应拦截器
- 错误处理机制
- 类型安全封装

**配置结构**:
```typescript
interface HttpConfig {
  baseURL: string;
  timeout: number;
  headers: Record<string, string>;
  withCredentials: boolean;
}

interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}
```

**拦截器实现**:
```typescript
// 请求拦截器
instance.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = getAuthToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求ID用于追踪
    config.headers['X-Request-ID'] = generateRequestId()
    
    return config
  },
  (error) => Promise.reject(error)
)

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    // 统一响应格式处理
    return response.data
  },
  (error) => {
    // 统一错误处理
    handleApiError(error)
    return Promise.reject(error)
  }
)
```

## 🎯 API模块详解

### task.ts - 任务管理API

**职责范围**: 学习任务的CRUD操作和状态管理

**接口定义**:
```typescript
interface TaskAPI {
  // 任务列表
  getTasks(params: TaskQueryParams): Promise<TaskListResponse>;
  
  // 任务详情
  getTask(id: string): Promise<TaskDetailResponse>;
  
  // 创建任务
  createTask(data: CreateTaskRequest): Promise<TaskResponse>;
  
  // 更新任务
  updateTask(id: string, data: UpdateTaskRequest): Promise<TaskResponse>;
  
  // 删除任务
  deleteTask(id: string): Promise<void>;
  
  // 批量操作
  batchUpdateTasks(data: BatchUpdateRequest): Promise<BatchResponse>;
  
  // 任务统计
  getTaskStatistics(params: StatisticsParams): Promise<TaskStatistics>;
}
```

**核心功能**:
- 任务列表查询 (支持分页、筛选、排序)
- 任务详情获取
- 任务创建和编辑
- 任务状态更新
- 批量任务操作
- 任务统计数据

**艾宾浩斯集成**:
- 任务难度评估接口
- 学习进度跟踪接口
- 复习计划生成接口

### review.ts - 复习管理API

**职责范围**: 复习会话管理和艾宾浩斯算法数据

**接口定义**:
```typescript
interface ReviewAPI {
  // 复习会话
  startReviewSession(data: StartSessionRequest): Promise<ReviewSession>;
  endReviewSession(sessionId: string): Promise<SessionResult>;
  
  // 复习记录
  submitReview(data: ReviewSubmission): Promise<ReviewResult>;
  getReviewHistory(params: HistoryParams): Promise<ReviewHistory>;
  
  // 艾宾浩斯数据
  getMemoryStrength(taskId: string): Promise<MemoryStrengthData>;
  updateMemoryStrength(data: MemoryUpdate): Promise<void>;
  
  // 复习计划
  getReviewPlan(params: PlanParams): Promise<ReviewPlan>;
  generateReviewQueue(): Promise<ReviewQueue>;
  
  // 统计分析
  getReviewStatistics(params: StatsParams): Promise<ReviewStatistics>;
}
```

**算法支持**:
- 遗忘曲线计算
- 间隔重复算法
- 记忆强度评估
- 最佳复习时间预测

### mindmap.ts - 思维导图API

**职责范围**: 思维导图数据管理和协作功能

**接口定义**:
```typescript
interface MindMapAPI {
  // 思维导图CRUD
  getMindMaps(params: MindMapQuery): Promise<MindMapList>;
  getMindMap(id: string): Promise<MindMapDetail>;
  createMindMap(data: CreateMindMapRequest): Promise<MindMapResponse>;
  updateMindMap(id: string, data: UpdateMindMapRequest): Promise<MindMapResponse>;
  deleteMindMap(id: string): Promise<void>;
  
  // 节点操作
  addNode(mapId: string, data: NodeData): Promise<NodeResponse>;
  updateNode(nodeId: string, data: NodeUpdate): Promise<NodeResponse>;
  deleteNode(nodeId: string): Promise<void>;
  
  // 协作功能
  shareMap(mapId: string, data: ShareRequest): Promise<ShareResponse>;
  getCollaborators(mapId: string): Promise<CollaboratorList>;
  
  // 导入导出
  exportMap(mapId: string, format: ExportFormat): Promise<ExportResult>;
  importMap(data: ImportRequest): Promise<ImportResult>;
}
```

**特色功能**:
- 实时协作编辑
- 版本历史管理
- 多格式导入导出
- 模板库支持

### user.ts - 用户管理API

**职责范围**: 用户认证、个人信息和偏好设置

**接口定义**:
```typescript
interface UserAPI {
  // 认证相关
  login(credentials: LoginRequest): Promise<AuthResponse>;
  logout(): Promise<void>;
  refreshToken(): Promise<TokenResponse>;
  
  // 用户信息
  getProfile(): Promise<UserProfile>;
  updateProfile(data: ProfileUpdate): Promise<UserProfile>;
  
  // 偏好设置
  getPreferences(): Promise<UserPreferences>;
  updatePreferences(data: PreferencesUpdate): Promise<UserPreferences>;
  
  // 学习统计
  getUserStatistics(): Promise<UserStatistics>;
  getAchievements(): Promise<AchievementList>;
}
```

**安全特性**:
- JWT token管理
- 自动token刷新
- 权限验证
- 密码安全策略

### notification.ts - 通知系统API

**职责范围**: 消息通知和提醒管理

**接口定义**:
```typescript
interface NotificationAPI {
  // 通知列表
  getNotifications(params: NotificationQuery): Promise<NotificationList>;
  
  // 通知操作
  markAsRead(id: string): Promise<void>;
  markAllAsRead(): Promise<void>;
  deleteNotification(id: string): Promise<void>;
  
  // 通知设置
  getNotificationSettings(): Promise<NotificationSettings>;
  updateNotificationSettings(data: SettingsUpdate): Promise<NotificationSettings>;
  
  // 推送订阅
  subscribePush(data: PushSubscription): Promise<void>;
  unsubscribePush(): Promise<void>;
}
```

**通知类型**:
- 学习提醒
- 复习提醒
- 系统通知
- 社交互动

### analytics.ts - 分析统计API

**职责范围**: 学习数据分析和报表生成

**接口定义**:
```typescript
interface AnalyticsAPI {
  // 学习分析
  getLearningAnalytics(params: AnalyticsParams): Promise<LearningAnalytics>;
  getProgressReport(params: ReportParams): Promise<ProgressReport>;
  
  // 性能分析
  getPerformanceMetrics(params: MetricsParams): Promise<PerformanceMetrics>;
  getMemoryAnalysis(params: MemoryParams): Promise<MemoryAnalysis>;
  
  // 趋势分析
  getLearningTrends(params: TrendParams): Promise<TrendData>;
  getPredictiveAnalysis(params: PredictParams): Promise<PredictionData>;
  
  // 报表导出
  exportReport(params: ExportParams): Promise<ReportFile>;
}
```

**分析维度**:
- 学习时长统计
- 记忆效果分析
- 学习习惯洞察
- 进步趋势预测

## 📝 使用规范和最佳实践

### API调用模式

```typescript
// 在Store中使用API
import { taskAPI } from '@/services/api'

export const useTaskStore = defineStore('task', () => {
  const tasks = ref<Task[]>([])
  const loading = ref(false)
  
  const fetchTasks = async (params?: TaskQueryParams) => {
    try {
      loading.value = true
      const response = await taskAPI.getTasks(params)
      tasks.value = response.data
    } catch (error) {
      // 错误处理
      console.error('Failed to fetch tasks:', error)
    } finally {
      loading.value = false
    }
  }
  
  return { tasks, loading, fetchTasks }
})
```

### 错误处理策略

```typescript
// 统一错误处理
const handleApiError = (error: AxiosError) => {
  const { response } = error
  
  switch (response?.status) {
    case 401:
      // 未授权，跳转登录
      router.push('/login')
      break
    case 403:
      // 权限不足
      ElMessage.error('权限不足')
      break
    case 500:
      // 服务器错误
      ElMessage.error('服务器错误，请稍后重试')
      break
    default:
      // 其他错误
      ElMessage.error(response?.data?.message || '请求失败')
  }
}
```

### 类型定义规范

```typescript
// 请求参数类型
interface TaskQueryParams {
  page?: number;
  pageSize?: number;
  status?: TaskStatus;
  priority?: TaskPriority;
  keyword?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 响应数据类型
interface TaskListResponse {
  data: Task[];
  total: number;
  page: number;
  pageSize: number;
}

// API方法类型
type TaskAPIMethod = (params?: TaskQueryParams) => Promise<TaskListResponse>;
```

## 🧪 测试策略

### API Mock测试

```typescript
// 使用MSW进行API模拟
import { rest } from 'msw'

export const handlers = [
  rest.get('/api/tasks', (req, res, ctx) => {
    return res(
      ctx.json({
        code: 200,
        data: mockTasks,
        message: 'success'
      })
    )
  })
]
```

### 集成测试

```typescript
// API集成测试
describe('Task API', () => {
  it('should fetch tasks successfully', async () => {
    const response = await taskAPI.getTasks()
    
    expect(response.code).toBe(200)
    expect(Array.isArray(response.data)).toBe(true)
  })
})
```

## 🔧 开发工具支持

### API文档生成
- 基于TypeScript类型自动生成API文档
- Swagger/OpenAPI集成
- 接口变更检测

### 调试工具
- 网络请求监控
- 响应时间分析
- 错误日志收集

## 📊 性能优化

### 1. 请求缓存
```typescript
// 使用SWR进行数据缓存
const { data, error } = useSWR('/api/tasks', taskAPI.getTasks)
```

### 2. 请求去重
```typescript
// 防止重复请求
const pendingRequests = new Map()

const request = (url: string, config: any) => {
  const key = `${url}_${JSON.stringify(config)}`
  
  if (pendingRequests.has(key)) {
    return pendingRequests.get(key)
  }
  
  const promise = axios(url, config)
  pendingRequests.set(key, promise)
  
  promise.finally(() => {
    pendingRequests.delete(key)
  })
  
  return promise
}
```

### 3. 分页优化
```typescript
// 虚拟滚动分页
const useInfiniteScroll = (api: Function) => {
  const data = ref([])
  const hasMore = ref(true)
  
  const loadMore = async () => {
    if (!hasMore.value) return
    
    const response = await api({
      page: Math.ceil(data.value.length / 20) + 1
    })
    
    data.value.push(...response.data)
    hasMore.value = response.data.length === 20
  }
  
  return { data, loadMore, hasMore }
}
```

---

**维护团队**: 前端开发团队  
**文档版本**: v1.0  
**更新时间**: 2025-08-03
