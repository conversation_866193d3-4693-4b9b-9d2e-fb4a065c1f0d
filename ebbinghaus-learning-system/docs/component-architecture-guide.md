# JYZS前端组件架构指南

## 📋 组件体系概览

本文档详细说明JYZS前端项目的组件架构设计，包括组件分类、设计原则、使用规范和最佳实践。

## 🏗️ 组件分层架构

### 组件分类体系

```
components/
├── layout/           # 布局组件层
│   ├── AppLayout.vue      # 主布局容器
│   ├── AppHeader.vue      # 顶部导航栏
│   ├── AppSidebar.vue     # 侧边导航栏
│   └── AppNotifications.vue # 通知组件
├── business/         # 业务组件层
│   ├── TaskCard.vue       # 任务卡片
│   ├── TaskList.vue       # 任务列表
│   ├── ReviewCard.vue     # 复习卡片
│   ├── MindMapCard.vue    # 思维导图卡片
│   ├── MindMapViewer.vue  # 思维导图查看器
│   ├── MindMapNode.vue    # 思维导图节点
│   └── MindMapEdge.vue    # 思维导图连线
├── common/           # 通用组件层 (待扩展)
└── mindmap/          # 思维导图专用组件 (待扩展)
```

## 🎯 布局组件层 (Layout Components)

### AppLayout.vue - 主布局容器
**职责**: 应用主体布局结构
**特点**:
- 响应式布局设计
- 集成Element Plus Layout组件
- 支持侧边栏折叠/展开
- 统一的页面容器管理

**使用场景**: 所有页面的根布局组件

### AppHeader.vue - 顶部导航栏
**职责**: 全局导航和用户操作
**功能模块**:
- Logo和应用标题
- 主导航菜单
- 用户头像和下拉菜单
- 通知图标和消息提示

**状态依赖**: 依赖user store获取用户信息

### AppSidebar.vue - 侧边导航栏
**职责**: 页面级导航和功能入口
**功能特点**:
- 多级菜单支持
- 路由高亮显示
- 折叠状态管理
- 权限控制显示

**路由集成**: 与Vue Router深度集成

### AppNotifications.vue - 通知组件
**职责**: 全局消息通知管理
**通知类型**:
- 系统通知
- 学习提醒
- 复习提醒
- 错误提示

## 📋 业务组件层 (Business Components)

### 任务管理组件

#### TaskCard.vue - 任务卡片
**设计目标**: 展示单个学习任务的核心信息
**数据结构**:
```typescript
interface TaskCardProps {
  task: Task;
  showActions?: boolean;
  compact?: boolean;
}
```

**功能特性**:
- 任务状态可视化
- 进度条显示
- 快速操作按钮
- 优先级标识

#### TaskList.vue - 任务列表
**设计目标**: 批量展示和管理任务
**组件组合**: 内部使用多个TaskCard组件
**功能特性**:
- 分页和虚拟滚动
- 排序和筛选
- 批量操作
- 拖拽排序

### 复习管理组件

#### ReviewCard.vue - 复习卡片
**设计目标**: 展示复习会话信息
**艾宾浩斯集成**: 
- 记忆曲线可视化
- 复习间隔计算
- 遗忘概率显示
- 最佳复习时间提醒

**交互设计**:
- 卡片翻转效果
- 难度评级按钮
- 进度跟踪
- 统计信息展示

### 思维导图组件

#### MindMapCard.vue - 思维导图卡片
**设计目标**: 思维导图的缩略图展示
**功能特性**:
- 缩略图预览
- 基本信息展示
- 快速操作入口
- 分享和导出功能

#### MindMapViewer.vue - 思维导图查看器
**设计目标**: 完整的思维导图展示和交互
**技术实现**:
- Canvas/SVG渲染
- 缩放和平移
- 节点编辑
- 连线管理

**性能优化**:
- 虚拟化渲染
- 懒加载节点
- 内存管理
- 事件防抖

#### MindMapNode.vue - 思维导图节点
**设计目标**: 单个思维导图节点的渲染
**节点类型**:
- 根节点
- 分支节点
- 叶子节点
- 特殊节点 (图片、链接等)

**样式系统**:
- 主题切换支持
- 自定义样式
- 动画效果
- 状态指示

#### MindMapEdge.vue - 思维导图连线
**设计目标**: 节点间连接线的渲染
**连线类型**:
- 直线连接
- 曲线连接
- 折线连接
- 自定义路径

## 🔧 组件设计原则

### 1. 单一职责原则
每个组件只负责一个明确的功能领域，避免功能耦合。

### 2. 组合优于继承
通过组件组合实现复杂功能，而不是通过继承。

### 3. Props向下，Events向上
- 父组件通过props传递数据给子组件
- 子组件通过events通知父组件状态变化

### 4. 状态管理分离
- 组件内部状态：使用ref/reactive
- 跨组件状态：使用Pinia stores
- 临时状态：使用provide/inject

## 📝 组件开发规范

### TypeScript类型定义
```typescript
// 组件Props接口定义
interface ComponentProps {
  // 必需属性
  data: DataType;
  // 可选属性
  options?: OptionsType;
  // 事件回调
  onUpdate?: (value: any) => void;
}

// 组件Emits定义
interface ComponentEmits {
  update: [value: any];
  change: [oldValue: any, newValue: any];
}
```

### 组件文件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入依赖
import { ref, computed, onMounted } from 'vue'
import type { ComponentProps } from '@/types'

// Props定义
const props = defineProps<ComponentProps>()

// Emits定义
const emit = defineEmits<ComponentEmits>()

// 响应式数据
const state = ref()

// 计算属性
const computedValue = computed(() => {})

// 方法定义
const handleAction = () => {}

// 生命周期
onMounted(() => {})
</script>

<style scoped>
/* 组件样式 */
</style>
```

### 命名规范
- **组件名**: PascalCase (如: TaskCard)
- **文件名**: PascalCase.vue (如: TaskCard.vue)
- **Props**: camelCase (如: showActions)
- **Events**: kebab-case (如: update-task)
- **CSS类名**: kebab-case (如: task-card)

## 🎨 样式设计规范

### CSS变量系统
```css
:root {
  /* 主题色彩 */
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  
  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}
```

### 响应式设计
```css
/* 移动端优先 */
.component {
  /* 基础样式 */
}

/* 平板端 */
@media (min-width: 768px) {
  .component {
    /* 平板样式 */
  }
}

/* 桌面端 */
@media (min-width: 1024px) {
  .component {
    /* 桌面样式 */
  }
}
```

## 🔄 组件通信模式

### 1. 父子组件通信
```typescript
// 父组件
<TaskCard 
  :task="taskData" 
  @update="handleTaskUpdate"
/>

// 子组件
const emit = defineEmits<{
  update: [task: Task]
}>()

const updateTask = () => {
  emit('update', updatedTask)
}
```

### 2. 跨组件通信
```typescript
// 使用Pinia Store
import { useTaskStore } from '@/stores/task'

const taskStore = useTaskStore()
const tasks = computed(() => taskStore.tasks)
```

### 3. 事件总线 (谨慎使用)
```typescript
// 全局事件
import { EventBus } from '@/utils/eventBus'

// 发送事件
EventBus.emit('task-updated', taskData)

// 监听事件
EventBus.on('task-updated', handleTaskUpdate)
```

## 🧪 组件测试策略

### 单元测试
- 使用Vitest进行组件单元测试
- 测试组件的props、events、computed
- 模拟用户交互行为

### 集成测试
- 测试组件间的协作
- 验证状态管理集成
- API调用模拟测试

### 视觉回归测试
- 组件快照测试
- 样式变更检测
- 跨浏览器兼容性

## 📈 性能优化指南

### 1. 组件懒加载
```typescript
const TaskCard = defineAsyncComponent(() => import('./TaskCard.vue'))
```

### 2. 计算属性缓存
```typescript
const expensiveValue = computed(() => {
  // 复杂计算逻辑
  return heavyComputation(props.data)
})
```

### 3. 事件防抖
```typescript
import { debounce } from '@/utils'

const handleSearch = debounce((query: string) => {
  // 搜索逻辑
}, 300)
```

### 4. 虚拟滚动
对于大量数据的列表组件，使用虚拟滚动技术优化性能。

---

**文档维护**: 前端开发团队  
**更新时间**: 2025-08-03  
**版本**: v1.0
