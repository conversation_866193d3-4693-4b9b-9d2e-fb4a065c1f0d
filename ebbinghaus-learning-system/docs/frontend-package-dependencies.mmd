graph LR
classDef package-node fill:#a29bfe,stroke:#2d3436,shape:ellipse
classDef package-scope-node fill:#ffeaa7,stroke:#2d3436,shape:stadium

  %% Package Scopes

  %% Node Definitions & Styles
  node7["env.d.ts"];
  style node7 fill:#81ecec,stroke:#333,stroke-width:1px
  node8["vue v\^3.4.0"];
  style node8 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node8 package-node
  node17["main.ts"];
  style node17 fill:#ff7675,stroke:#333,stroke-width:1px
  node18["pinia v\^2.1.7"];
  style node18 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node18 package-node
  node19["element-plus v\^2.4.4"];
  style node19 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node19 package-node
  node20["element-plus v\^2.4.4"];
  style node20 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node20 package-node
  node21["_element-plus_icons-vue v\^..."];
  style node21 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node21 package-node
  node35["vite.config.ts"];
  style node35 fill:#81ecec,stroke:#333,stroke-width:1px
  node36["vite v\^5.0.10 \[dev\]"];
  style node36 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node36 package-node
  node37["_vitejs_plugin-vue v\^4.5.2..."];
  style node37 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node37 package-node
  node40["node_url"];
  style node40 fill:#a29bfe,stroke:#333,stroke-width:1px
  class node40 package-node

  %% Edge Definitions
  node7 --> node8
  linkStyle 0 stroke:#6c5ce7,stroke-width:1.5px
  node17 --> node8
  linkStyle 1 stroke:#6c5ce7,stroke-width:1.5px
  node17 --> node18
  linkStyle 2 stroke:#6c5ce7,stroke-width:1.5px
  node17 --> node19
  linkStyle 3 stroke:#6c5ce7,stroke-width:1.5px
  node17 --> node20
  linkStyle 4 stroke:#6c5ce7,stroke-width:1.5px
  node17 --> node21
  linkStyle 5 stroke:#6c5ce7,stroke-width:1.5px
  node35 --> node36
  linkStyle 6 stroke:#6c5ce7,stroke-width:1.5px
  node35 --> node37
  linkStyle 7 stroke:#6c5ce7,stroke-width:1.5px
  node35 --> node40
  linkStyle 8 stroke:#6c5ce7,stroke-width:1.5px