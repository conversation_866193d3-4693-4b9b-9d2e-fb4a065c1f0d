# JYZS状态管理架构文档

## 📋 状态管理概览

JYZS项目采用Pinia作为状态管理解决方案，构建了模块化、类型安全的状态管理架构。本文档详细说明状态管理的设计理念、模块结构和使用规范。

## 🏗️ Pinia架构设计

### 核心设计原则

1. **模块化分离**: 按业务领域划分独立的store模块
2. **类型安全**: 完整的TypeScript类型定义
3. **组合式API**: 使用Composition API风格
4. **依赖管理**: 清晰的store间依赖关系
5. **开发体验**: 支持热更新和时间旅行调试

### Store模块架构

```
stores/
├── index.ts          # Store聚合导出
├── app.ts           # 应用全局状态 (重要性: 10)
├── user.ts          # 用户状态管理 (重要性: 8)
├── task.ts          # 任务状态管理 (重要性: 8)
├── review.ts        # 复习状态管理 (重要性: 8)
└── mindmap.ts       # 思维导图状态 (重要性: 8)
```

## 🎯 核心Store模块

### app.ts - 应用全局状态

**职责范围**: 应用级别的全局状态管理
**重要性**: 10 (被所有业务store依赖)

**状态结构**:
```typescript
interface AppState {
  // 应用基础信息
  appName: string;
  version: string;
  
  // UI状态
  loading: boolean;
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark';
  
  // 系统配置
  locale: string;
  timezone: string;
  
  // 通知系统
  notifications: Notification[];
  
  // 错误处理
  errors: AppError[];
}
```

**核心功能**:
- 全局加载状态管理
- 主题切换控制
- 侧边栏状态管理
- 通知消息管理
- 错误状态收集
- 应用配置管理

**依赖关系**: 作为基础store，被其他所有业务store依赖

### user.ts - 用户状态管理

**职责范围**: 用户认证、个人信息和偏好设置
**依赖**: app.ts

**状态结构**:
```typescript
interface UserState {
  // 认证信息
  isAuthenticated: boolean;
  token: string | null;
  refreshToken: string | null;
  
  // 用户信息
  profile: UserProfile | null;
  permissions: string[];
  roles: string[];
  
  // 学习偏好
  learningPreferences: LearningPreferences;
  
  // 统计数据
  statistics: UserStatistics;
}
```

**核心Actions**:
- `login()`: 用户登录
- `logout()`: 用户登出
- `updateProfile()`: 更新个人信息
- `updatePreferences()`: 更新学习偏好
- `refreshToken()`: 刷新认证令牌

**API集成**: 调用user.ts API模块

### task.ts - 任务状态管理

**职责范围**: 学习任务的创建、管理和跟踪
**依赖**: app.ts

**状态结构**:
```typescript
interface TaskState {
  // 任务列表
  tasks: Task[];
  currentTask: Task | null;
  
  // 筛选和排序
  filters: TaskFilters;
  sortBy: TaskSortOption;
  
  // 分页信息
  pagination: PaginationInfo;
  
  // 任务统计
  statistics: TaskStatistics;
  
  // 加载状态
  loading: {
    list: boolean;
    detail: boolean;
    create: boolean;
    update: boolean;
    delete: boolean;
  };
}
```

**核心Actions**:
- `fetchTasks()`: 获取任务列表
- `createTask()`: 创建新任务
- `updateTask()`: 更新任务
- `deleteTask()`: 删除任务
- `setCurrentTask()`: 设置当前任务
- `updateFilters()`: 更新筛选条件

**艾宾浩斯集成**:
- 任务难度评估
- 复习间隔计算
- 记忆强度跟踪

### review.ts - 复习状态管理

**职责范围**: 复习会话管理和艾宾浩斯算法实现
**依赖**: app.ts

**状态结构**:
```typescript
interface ReviewState {
  // 复习会话
  currentSession: ReviewSession | null;
  sessions: ReviewSession[];
  
  // 复习队列
  reviewQueue: ReviewItem[];
  completedReviews: ReviewItem[];
  
  // 艾宾浩斯数据
  memoryStrength: Map<string, number>;
  forgettingCurve: ForgettingCurveData;
  
  // 复习统计
  statistics: ReviewStatistics;
  
  // 会话状态
  sessionActive: boolean;
  currentIndex: number;
  totalItems: number;
}
```

**核心Actions**:
- `startReviewSession()`: 开始复习会话
- `submitReview()`: 提交复习结果
- `endSession()`: 结束复习会话
- `calculateNextReview()`: 计算下次复习时间
- `updateMemoryStrength()`: 更新记忆强度
- `generateReviewQueue()`: 生成复习队列

**算法实现**:
- 艾宾浩斯遗忘曲线
- 间隔重复算法
- 难度系数调整
- 记忆强度计算

### mindmap.ts - 思维导图状态

**职责范围**: 思维导图数据管理和可视化状态
**依赖**: app.ts

**状态结构**:
```typescript
interface MindMapState {
  // 思维导图数据
  mindmaps: MindMap[];
  currentMindmap: MindMap | null;
  
  // 编辑状态
  editMode: boolean;
  selectedNodes: string[];
  clipboard: MindMapNode[];
  
  // 视图状态
  viewport: ViewportState;
  zoom: number;
  center: Point;
  
  // 历史记录
  history: MindMapHistory[];
  historyIndex: number;
  
  // 协作状态
  collaborators: Collaborator[];
  conflicts: ConflictInfo[];
}
```

**核心Actions**:
- `createMindMap()`: 创建思维导图
- `updateMindMap()`: 更新思维导图
- `addNode()`: 添加节点
- `deleteNode()`: 删除节点
- `connectNodes()`: 连接节点
- `undo()` / `redo()`: 撤销/重做
- `exportMindMap()`: 导出思维导图

**可视化支持**:
- 节点位置计算
- 连线路径生成
- 布局算法集成
- 动画状态管理

## 🔗 Store间依赖关系

### 依赖图谱

```mermaid
graph TD
    A[app.ts] --> B[user.ts]
    A --> C[task.ts]
    A --> D[review.ts]
    A --> E[mindmap.ts]
    
    B -.-> C
    C -.-> D
    C -.-> E
```

**依赖说明**:
- **强依赖** (实线): 直接导入和使用
- **弱依赖** (虚线): 数据关联但不直接导入

### 跨Store通信

**1. 直接依赖**:
```typescript
// 在业务store中使用app store
import { useAppStore } from './app'

export const useTaskStore = defineStore('task', () => {
  const appStore = useAppStore()
  
  const setLoading = (loading: boolean) => {
    appStore.setLoading(loading)
  }
})
```

**2. 事件通信**:
```typescript
// 使用watch监听其他store变化
watch(
  () => userStore.isAuthenticated,
  (authenticated) => {
    if (!authenticated) {
      // 清空任务数据
      tasks.value = []
    }
  }
)
```

## 📝 使用规范和最佳实践

### Store定义规范

```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { StoreState } from '@/types'

export const useExampleStore = defineStore('example', () => {
  // 状态定义
  const state = ref<StoreState>({})
  
  // 计算属性 (Getters)
  const computedValue = computed(() => {
    return state.value.someProperty
  })
  
  // 方法定义 (Actions)
  const updateState = async (data: any) => {
    try {
      // 业务逻辑
      state.value = { ...state.value, ...data }
    } catch (error) {
      // 错误处理
      console.error('Update failed:', error)
    }
  }
  
  // 导出
  return {
    // 状态
    state: readonly(state),
    
    // 计算属性
    computedValue,
    
    // 方法
    updateState
  }
})
```

### 组件中使用Store

```typescript
<script setup lang="ts">
import { useTaskStore } from '@/stores/task'
import { storeToRefs } from 'pinia'

// 获取store实例
const taskStore = useTaskStore()

// 响应式解构 (保持响应性)
const { tasks, loading } = storeToRefs(taskStore)

// 方法可以直接解构
const { fetchTasks, createTask } = taskStore

// 组件挂载时获取数据
onMounted(() => {
  fetchTasks()
})
</script>
```

### 错误处理模式

```typescript
const handleAsyncAction = async () => {
  const appStore = useAppStore()
  
  try {
    appStore.setLoading(true)
    await someAsyncOperation()
  } catch (error) {
    appStore.addError({
      message: '操作失败',
      type: 'error',
      timestamp: Date.now()
    })
  } finally {
    appStore.setLoading(false)
  }
}
```

## 🧪 测试策略

### Store单元测试

```typescript
import { createPinia, setActivePinia } from 'pinia'
import { useTaskStore } from '@/stores/task'

describe('Task Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })
  
  it('should create task correctly', async () => {
    const store = useTaskStore()
    const taskData = { title: 'Test Task' }
    
    await store.createTask(taskData)
    
    expect(store.tasks).toHaveLength(1)
    expect(store.tasks[0].title).toBe('Test Task')
  })
})
```

### 集成测试

```typescript
// 测试store间的协作
it('should update app loading when fetching tasks', async () => {
  const appStore = useAppStore()
  const taskStore = useTaskStore()
  
  const fetchPromise = taskStore.fetchTasks()
  expect(appStore.loading).toBe(true)
  
  await fetchPromise
  expect(appStore.loading).toBe(false)
})
```

## 🔧 开发工具集成

### Pinia DevTools

- 状态时间旅行调试
- Action执行跟踪
- 状态变更历史
- 性能分析工具

### 热更新支持

```typescript
// 开发环境热更新
if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useTaskStore, import.meta.hot))
}
```

## 📊 性能优化

### 1. 状态分片
将大型状态对象分解为更小的响应式片段。

### 2. 计算属性缓存
充分利用computed的缓存机制。

### 3. 异步状态管理
```typescript
const { data, pending, error } = await $fetch('/api/tasks')
```

### 4. 状态持久化
```typescript
// 使用pinia-plugin-persistedstate
export const useUserStore = defineStore('user', () => {
  // store定义
}, {
  persist: {
    key: 'user-store',
    storage: localStorage
  }
})
```

---

**维护团队**: 前端开发团队  
**文档版本**: v1.0  
**更新时间**: 2025-08-03
